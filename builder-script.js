// AI Resume Builder - Main JavaScript File

class ResumeBuilder {
    constructor() {
        this.data = {
            personal: {},
            summary: '',
            experience: [],
            education: [],
            skills: [],
            apiKey: ''
        };

        this.currentTemplate = 'modern';
        this.init();
    }

    init() {
        this.loadSavedData();
        this.bindEvents();
        this.addInitialExperience();
        this.addInitialEducation();
    }

    bindEvents() {
        // API Key toggle and save
        document.getElementById('toggleApiKey').addEventListener('click', this.toggleApiKeyVisibility);
        document.getElementById('apiKey').addEventListener('input', (e) => {
            this.data.apiKey = e.target.value;
            localStorage.setItem('resumeBuilderApiKey', e.target.value);
        });

        // Form inputs
        this.bindPersonalInfoEvents();
        this.bindSummaryEvents();
        this.bindSkillsEvents();
        this.bindExperienceEvents();
        this.bindEducationEvents();

        // AI features
        document.getElementById('generateSummary').addEventListener('click', () => this.generateSummary());
        document.getElementById('suggestSkills').addEventListener('click', () => this.suggestSkills());
        document.getElementById('optimizeResume').addEventListener('click', () => this.optimizeResume());

        // Template selection
        document.getElementById('templateSelect').addEventListener('change', (e) => {
            this.currentTemplate = e.target.value;
            this.updatePreview();
        });

        // Save and export
        document.getElementById('saveBtn').addEventListener('click', () => this.saveData());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportPDF());

        // Auto-save on input changes
        this.setupAutoSave();
    }

    bindPersonalInfoEvents() {
        const personalFields = ['fullName', 'jobTitle', 'email', 'phone', 'location', 'linkedin', 'website'];
        personalFields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                element.addEventListener('input', (e) => {
                    this.data.personal[field] = e.target.value;
                    this.updatePreview();
                });
            }
        });
    }

    bindSummaryEvents() {
        document.getElementById('summary').addEventListener('input', (e) => {
            this.data.summary = e.target.value;
            this.updatePreview();
        });
    }

    bindSkillsEvents() {
        const skillInput = document.getElementById('skillInput');
        skillInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.target.value.trim()) {
                this.addSkill(e.target.value.trim());
                e.target.value = '';
            }
        });
    }

    bindExperienceEvents() {
        document.getElementById('addExperience').addEventListener('click', () => this.addExperience());
    }

    bindEducationEvents() {
        document.getElementById('addEducation').addEventListener('click', () => this.addEducation());
    }

    toggleApiKeyVisibility() {
        const apiKeyInput = document.getElementById('apiKey');
        const toggleBtn = document.getElementById('toggleApiKey');
        const icon = toggleBtn.querySelector('i');

        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            apiKeyInput.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    addSkill(skillName) {
        if (!this.data.skills.includes(skillName)) {
            this.data.skills.push(skillName);
            this.renderSkills();
            this.updatePreview();
        }
    }

    removeSkill(skillName) {
        this.data.skills = this.data.skills.filter(skill => skill !== skillName);
        this.renderSkills();
        this.updatePreview();
    }

    renderSkills() {
        const container = document.getElementById('skillsContainer');
        container.innerHTML = this.data.skills.map(skill => `
            <div class="skill-tag">
                ${skill}
                <button class="remove-skill" onclick="resumeBuilder.removeSkill('${skill}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }

    addExperience() {
        const experience = {
            id: Date.now(),
            title: '',
            company: '',
            location: '',
            startDate: '',
            endDate: '',
            current: false,
            description: ''
        };

        this.data.experience.push(experience);
        this.renderExperience();
    }

    addInitialExperience() {
        if (this.data.experience.length === 0) {
            this.addExperience();
        } else {
            this.renderExperience();
        }
    }

    renderExperience() {
        const container = document.getElementById('experienceContainer');
        container.innerHTML = this.data.experience.map(exp => this.createExperienceHTML(exp)).join('');
    }

    createExperienceHTML(exp) {
        return `
            <div class="experience-item" data-id="${exp.id}">
                <div class="form-grid">
                    <input type="text" placeholder="Job Title" value="${exp.title}"
                           onchange="resumeBuilder.updateExperience(${exp.id}, 'title', this.value)">
                    <input type="text" placeholder="Company" value="${exp.company}"
                           onchange="resumeBuilder.updateExperience(${exp.id}, 'company', this.value)">
                    <input type="text" placeholder="Location" value="${exp.location}"
                           onchange="resumeBuilder.updateExperience(${exp.id}, 'location', this.value)">
                    <input type="month" placeholder="Start Date" value="${exp.startDate}"
                           onchange="resumeBuilder.updateExperience(${exp.id}, 'startDate', this.value)">
                    <input type="month" placeholder="End Date" value="${exp.endDate}"
                           ${exp.current ? 'disabled' : ''}
                           onchange="resumeBuilder.updateExperience(${exp.id}, 'endDate', this.value)">
                    <label>
                        <input type="checkbox" ${exp.current ? 'checked' : ''}
                               onchange="resumeBuilder.updateExperience(${exp.id}, 'current', this.checked)">
                        Current Position
                    </label>
                </div>
                <textarea placeholder="Job description and achievements..." rows="3"
                          onchange="resumeBuilder.updateExperience(${exp.id}, 'description', this.value)">${exp.description}</textarea>
                <div class="item-actions">
                    <button class="btn btn-ai" onclick="resumeBuilder.enhanceJobDescription(${exp.id})">
                        <i class="fas fa-magic"></i> Enhance with AI
                    </button>
                    <button class="btn btn-secondary" onclick="resumeBuilder.removeExperience(${exp.id})">
                        <i class="fas fa-trash"></i> Remove
                    </button>
                </div>
            </div>
        `;
    }

    updateExperience(id, field, value) {
        const exp = this.data.experience.find(e => e.id === id);
        if (exp) {
            exp[field] = value;
            if (field === 'current' && value) {
                exp.endDate = '';
                this.renderExperience();
            }
            this.updatePreview();
        }
    }

    removeExperience(id) {
        this.data.experience = this.data.experience.filter(e => e.id !== id);
        this.renderExperience();
        this.updatePreview();
    }

    addEducation() {
        const education = {
            id: Date.now(),
            degree: '',
            school: '',
            location: '',
            graduationDate: '',
            gpa: '',
            description: ''
        };

        this.data.education.push(education);
        this.renderEducation();
    }

    addInitialEducation() {
        if (this.data.education.length === 0) {
            this.addEducation();
        } else {
            this.renderEducation();
        }
    }

    renderEducation() {
        const container = document.getElementById('educationContainer');
        container.innerHTML = this.data.education.map(edu => this.createEducationHTML(edu)).join('');
    }

    createEducationHTML(edu) {
        return `
            <div class="education-item" data-id="${edu.id}">
                <div class="form-grid">
                    <input type="text" placeholder="Degree" value="${edu.degree}"
                           onchange="resumeBuilder.updateEducation(${edu.id}, 'degree', this.value)">
                    <input type="text" placeholder="School/University" value="${edu.school}"
                           onchange="resumeBuilder.updateEducation(${edu.id}, 'school', this.value)">
                    <input type="text" placeholder="Location" value="${edu.location}"
                           onchange="resumeBuilder.updateEducation(${edu.id}, 'location', this.value)">
                    <input type="month" placeholder="Graduation Date" value="${edu.graduationDate}"
                           onchange="resumeBuilder.updateEducation(${edu.id}, 'graduationDate', this.value)">
                    <input type="text" placeholder="GPA (optional)" value="${edu.gpa}"
                           onchange="resumeBuilder.updateEducation(${edu.id}, 'gpa', this.value)">
                </div>
                <textarea placeholder="Additional details, honors, relevant coursework..." rows="2"
                          onchange="resumeBuilder.updateEducation(${edu.id}, 'description', this.value)">${edu.description}</textarea>
                <button class="btn btn-secondary" onclick="resumeBuilder.removeEducation(${edu.id})">
                    <i class="fas fa-trash"></i> Remove
                </button>
            </div>
        `;
    }

    updateEducation(id, field, value) {
        const edu = this.data.education.find(e => e.id === id);
        if (edu) {
            edu[field] = value;
            this.updatePreview();
        }
    }

    removeEducation(id) {
        this.data.education = this.data.education.filter(e => e.id !== id);
        this.renderEducation();
        this.updatePreview();
    }

    // AI Functions
    async generateSummary() {
        const apiKey = document.getElementById('apiKey').value;
        if (!apiKey) {
            this.showToast('Please enter your OpenRouter API key first', 'error');
            return;
        }

        this.showLoading('Generating professional summary...');

        try {
            const prompt = this.buildSummaryPrompt();
            const response = await this.callOpenRouter(apiKey, prompt);

            if (response) {
                document.getElementById('summary').value = response;
                this.data.summary = response;
                this.updatePreview();
                this.showToast('Professional summary generated successfully!');
            }
        } catch (error) {
            console.error('Error generating summary:', error);
            this.showToast('Failed to generate summary. Please check your API key.', 'error');
        }

        this.hideLoading();
    }

    async suggestSkills() {
        const apiKey = document.getElementById('apiKey').value;
        if (!apiKey) {
            this.showToast('Please enter your OpenRouter API key first', 'error');
            return;
        }

        this.showLoading('Suggesting relevant skills...');

        try {
            const prompt = this.buildSkillsPrompt();
            const response = await this.callOpenRouter(apiKey, prompt);

            if (response) {
                const skills = response.split(',').map(s => s.trim()).filter(s => s);
                skills.forEach(skill => this.addSkill(skill));
                this.showToast(`Added ${skills.length} skill suggestions!`);
            }
        } catch (error) {
            console.error('Error suggesting skills:', error);
            this.showToast('Failed to suggest skills. Please check your API key.', 'error');
        }

        this.hideLoading();
    }

    async optimizeResume() {
        const apiKey = document.getElementById('apiKey').value;
        const jobDescription = document.getElementById('jobDescription').value;

        if (!apiKey) {
            this.showToast('Please enter your OpenRouter API key first', 'error');
            return;
        }

        if (!jobDescription.trim()) {
            this.showToast('Please paste a job description first', 'error');
            return;
        }

        this.showLoading('AI is analyzing job description and optimizing your entire resume...');

        try {
            const prompt = this.buildOptimizationPrompt(jobDescription);

            // Debug: Log the prompt being sent
            console.log('=== OPTIMIZATION PROMPT ===');
            console.log(prompt);
            console.log('=== END PROMPT ===');

            const response = await this.callOpenRouter(apiKey, prompt);

            // Debug: Log the raw response
            console.log('=== RAW LLM RESPONSE ===');
            console.log(response);
            console.log('=== END RAW RESPONSE ===');

            if (response) {
                // Parse the AI response and update relevant sections
                this.parseOptimizationResponse(response);
            } else {
                this.showToast('No response received from AI', 'error');
            }
        } catch (error) {
            console.error('Error optimizing resume:', error);
            this.showToast('Failed to optimize resume. Please check your API key.', 'error');
        }

        this.hideLoading();
    }

    async enhanceJobDescription(id) {
        const apiKey = document.getElementById('apiKey').value;
        if (!apiKey) {
            this.showToast('Please enter your OpenRouter API key first', 'error');
            return;
        }

        const experience = this.data.experience.find(e => e.id === id);
        if (!experience || !experience.description.trim()) {
            this.showToast('Please add a job description first', 'error');
            return;
        }

        this.showLoading('Enhancing job description...');

        try {
            const prompt = this.buildJobEnhancementPrompt(experience);
            const response = await this.callOpenRouter(apiKey, prompt);

            if (response) {
                experience.description = response;
                this.renderExperience();
                this.updatePreview();
                this.showToast('Job description enhanced successfully!');
            }
        } catch (error) {
            console.error('Error enhancing job description:', error);
            this.showToast('Failed to enhance job description. Please check your API key.', 'error');
        }

        this.hideLoading();
    }

    async callOpenRouter(apiKey, prompt) {
        console.log('=== API CALL DEBUG ===');
        console.log('API Key length:', apiKey.length);
        console.log('Prompt length:', prompt.length);

        const requestBody = {
            model: 'anthropic/claude-3.5-sonnet',
            messages: [
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: 2000, // Increased for longer responses
            temperature: 0.7
        };

        console.log('Request body:', JSON.stringify(requestBody, null, 2));

        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': window.location.origin,
                'X-Title': 'AI Resume Builder'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error Response:', errorText);
            throw new Error(`API request failed: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('Full API response:', JSON.stringify(data, null, 2));

        const content = data.choices[0]?.message?.content?.trim();
        console.log('Extracted content:', content);
        console.log('=== END API CALL DEBUG ===');

        return content;
    }

    buildSummaryPrompt() {
        const personal = this.data.personal;
        const experience = this.data.experience;

        return `Create a professional summary for a resume based on the following information:

Name: ${personal.fullName || 'Not provided'}
Job Title: ${personal.jobTitle || 'Not provided'}
Experience: ${experience.map(exp => `${exp.title} at ${exp.company}`).join(', ') || 'Not provided'}

Write a compelling 2-3 sentence professional summary that highlights key strengths, experience, and value proposition. Make it specific and impactful. Return only the summary text without any additional formatting or explanations.`;
    }

    buildSkillsPrompt() {
        const personal = this.data.personal;
        const experience = this.data.experience;

        return `Suggest 8-10 relevant professional skills for someone with this background:

Job Title: ${personal.jobTitle || 'Not provided'}
Experience: ${experience.map(exp => `${exp.title} at ${exp.company}: ${exp.description}`).join('\n') || 'Not provided'}

Return only a comma-separated list of skills, no explanations or additional text. Focus on technical skills, soft skills, and industry-specific competencies.`;
    }

    buildOptimizationPrompt(jobDescription) {
        return `You are an expert resume writer and ATS optimization specialist. Analyze the job description and completely optimize this resume to maximize relevance and ATS compatibility.

JOB DESCRIPTION:
${jobDescription}

CURRENT RESUME DATA:
Personal Info: ${JSON.stringify(this.data.personal, null, 2)}
Summary: ${this.data.summary || 'No summary provided'}
Experience: ${JSON.stringify(this.data.experience, null, 2)}
Education: ${JSON.stringify(this.data.education, null, 2)}
Skills: ${JSON.stringify(this.data.skills, null, 2)}

OPTIMIZATION REQUIREMENTS:
1. Rewrite the professional summary to directly address the job requirements
2. Enhance ALL experience descriptions with relevant keywords, quantified achievements, and transferable skills
3. Add missing skills that are mentioned in the job description and relevant to the candidate's background
4. Ensure ATS-friendly language and keyword density
5. Maintain authenticity while maximizing relevance

Return a complete optimized resume in this exact JSON format:
{
  "personal": {
    "fullName": "${this.data.personal.fullName || ''}",
    "jobTitle": "Optimized job title that matches the target role",
    "email": "${this.data.personal.email || ''}",
    "phone": "${this.data.personal.phone || ''}",
    "location": "${this.data.personal.location || ''}",
    "linkedin": "${this.data.personal.linkedin || ''}",
    "website": "${this.data.personal.website || ''}"
  },
  "summary": "Completely rewritten professional summary that directly addresses the job requirements and highlights relevant experience",
  "experience": [
    ${this.data.experience.map(exp => `{
      "id": ${exp.id},
      "title": "${exp.title || ''}",
      "company": "${exp.company || ''}",
      "location": "${exp.location || ''}",
      "startDate": "${exp.startDate || ''}",
      "endDate": "${exp.endDate || ''}",
      "current": ${exp.current || false},
      "description": "Enhanced description with job-relevant keywords, quantified achievements, and transferable skills"
    }`).join(',\n    ')}
  ],
  "education": [
    ${this.data.education.map(edu => `{
      "id": ${edu.id},
      "degree": "${edu.degree || ''}",
      "school": "${edu.school || ''}",
      "location": "${edu.location || ''}",
      "graduationDate": "${edu.graduationDate || ''}",
      "gpa": "${edu.gpa || ''}",
      "description": "Enhanced with relevant coursework or achievements if applicable"
    }`).join(',\n    ')}
  ],
  "skills": ["Include existing relevant skills plus new ones from job description"],
  "optimizationSummary": {
    "keyChanges": ["List of major changes made"],
    "keywordsAdded": ["Important keywords incorporated"],
    "atsScore": "Estimated ATS compatibility improvement"
  }
}

CRITICAL: Return ONLY valid JSON. No markdown, no explanations, no additional text.`;
    }

    buildJobEnhancementPrompt(experience) {
        return `Enhance this job description to be more impactful and achievement-focused:

Job Title: ${experience.title}
Company: ${experience.company}
Current Description: ${experience.description}

Rewrite the description to:
- Use strong action verbs
- Include quantifiable achievements where possible
- Highlight relevant skills and technologies
- Make it more compelling to employers

Return only the enhanced description, no additional formatting or explanations.`;
    }

    parseOptimizationResponse(response) {
        console.log('=== PARSING OPTIMIZATION RESPONSE ===');
        console.log('Current data before optimization:', JSON.stringify(this.data, null, 2));

        try {
            const optimizedResume = JSON.parse(response);
            console.log('Parsed optimized resume:', JSON.stringify(optimizedResume, null, 2));

            let changesApplied = [];

            // Update personal information (including optimized job title)
            if (optimizedResume.personal) {
                console.log('Processing personal info updates...');
                console.log('Current personal:', this.data.personal);
                console.log('Optimized personal:', optimizedResume.personal);

                Object.keys(optimizedResume.personal).forEach(key => {
                    const currentValue = this.data.personal[key] || '';
                    const optimizedValue = optimizedResume.personal[key] || '';

                    console.log(`Checking ${key}: "${currentValue}" vs "${optimizedValue}"`);

                    if (optimizedValue && optimizedValue !== currentValue) {
                        console.log(`Updating ${key} from "${currentValue}" to "${optimizedValue}"`);
                        this.data.personal[key] = optimizedValue;
                        const element = document.getElementById(key);
                        if (element) {
                            element.value = optimizedValue;
                        }
                        changesApplied.push(`${key}`);
                    }
                });
            }

            // Update professional summary
            if (optimizedResume.summary) {
                console.log('Processing summary update...');
                console.log('Current summary:', this.data.summary);
                console.log('Optimized summary:', optimizedResume.summary);

                if (optimizedResume.summary !== this.data.summary) {
                    console.log('Updating summary');
                    document.getElementById('summary').value = optimizedResume.summary;
                    this.data.summary = optimizedResume.summary;
                    changesApplied.push('Professional summary');
                } else {
                    console.log('Summary unchanged');
                }
            }

            // Update experience descriptions
            if (optimizedResume.experience && Array.isArray(optimizedResume.experience)) {
                console.log('Processing experience updates...');
                console.log('Current experience:', this.data.experience);
                console.log('Optimized experience:', optimizedResume.experience);

                let experienceUpdated = 0;
                optimizedResume.experience.forEach(optimizedExp => {
                    const currentExp = this.data.experience.find(exp => exp.id === optimizedExp.id);
                    console.log(`Checking experience ID ${optimizedExp.id}:`, {
                        found: !!currentExp,
                        currentDescription: currentExp?.description,
                        optimizedDescription: optimizedExp.description
                    });

                    if (currentExp && optimizedExp.description && optimizedExp.description !== currentExp.description) {
                        console.log(`Updating experience ${optimizedExp.id}`);
                        currentExp.description = optimizedExp.description;
                        experienceUpdated++;
                    }
                });

                if (experienceUpdated > 0) {
                    console.log(`Updated ${experienceUpdated} experience entries`);
                    changesApplied.push(`${experienceUpdated} job descriptions`);
                    this.renderExperience();
                } else {
                    console.log('No experience entries updated');
                }
            }

            // Update education descriptions
            if (optimizedResume.education && Array.isArray(optimizedResume.education)) {
                console.log('Processing education updates...');
                let educationUpdated = 0;
                optimizedResume.education.forEach(optimizedEdu => {
                    const currentEdu = this.data.education.find(edu => edu.id === optimizedEdu.id);
                    if (currentEdu && optimizedEdu.description && optimizedEdu.description !== currentEdu.description) {
                        currentEdu.description = optimizedEdu.description;
                        educationUpdated++;
                    }
                });

                if (educationUpdated > 0) {
                    console.log(`Updated ${educationUpdated} education entries`);
                    changesApplied.push(`${educationUpdated} education entries`);
                    this.renderEducation();
                }
            }

            // Update skills (replace with optimized list)
            if (optimizedResume.skills && Array.isArray(optimizedResume.skills)) {
                console.log('Processing skills update...');
                console.log('Current skills:', this.data.skills);
                console.log('Optimized skills:', optimizedResume.skills);

                const originalSkillsCount = this.data.skills.length;
                this.data.skills = [...optimizedResume.skills]; // Replace with optimized skills
                this.renderSkills();

                const skillsDiff = this.data.skills.length - originalSkillsCount;
                console.log(`Skills updated: ${originalSkillsCount} -> ${this.data.skills.length} (${skillsDiff > 0 ? '+' : ''}${skillsDiff})`);

                if (skillsDiff !== 0) {
                    changesApplied.push(`Skills updated (${skillsDiff > 0 ? '+' : ''}${skillsDiff})`);
                }
            }

            console.log('Changes applied:', changesApplied);
            this.updatePreview();

            // Show comprehensive success message
            const changesMessage = changesApplied.length > 0
                ? `✨ Resume optimized! Updated: ${changesApplied.join(', ')}`
                : '✨ Resume optimized for target role!';
            this.showToast(changesMessage, 'success');

            // Show optimization summary if available
            if (optimizedResume.optimizationSummary) {
                const summary = optimizedResume.optimizationSummary;
                console.log('Optimization summary:', summary);

                if (summary.keyChanges && summary.keyChanges.length > 0) {
                    setTimeout(() => {
                        this.showToast(`🎯 Key improvements: ${summary.keyChanges.join(', ')}`, 'info');
                    }, 2000);
                }

                if (summary.keywordsAdded && summary.keywordsAdded.length > 0) {
                    setTimeout(() => {
                        this.showToast(`🔑 Keywords added: ${summary.keywordsAdded.join(', ')}`, 'info');
                    }, 4000);
                }

                if (summary.atsScore) {
                    setTimeout(() => {
                        this.showToast(`📊 ${summary.atsScore}`, 'info');
                    }, 6000);
                }
            }

        } catch (error) {
            console.error('Error parsing optimization response:', error);
            console.log('Raw response that failed to parse:', response);
            this.handleFallbackOptimization(response);
        }

        console.log('=== END PARSING ===');
    }

    handleFallbackOptimization(response) {
        // If JSON parsing fails, try to extract useful information from the response
        const lines = response.split('\n');
        let foundUsefulContent = false;

        // Look for summary-like content
        const summaryKeywords = ['summary', 'professional', 'experienced', 'skilled'];
        const summaryLine = lines.find(line =>
            summaryKeywords.some(keyword => line.toLowerCase().includes(keyword)) &&
            line.length > 50
        );

        if (summaryLine && !this.data.summary.trim()) {
            document.getElementById('summary').value = summaryLine.trim();
            this.data.summary = summaryLine.trim();
            foundUsefulContent = true;
        }

        // Look for skills
        const skillsLine = lines.find(line =>
            line.toLowerCase().includes('skill') && line.includes(',')
        );

        if (skillsLine) {
            const potentialSkills = skillsLine.split(',').map(s => s.trim()).filter(s =>
                s.length > 2 && s.length < 30 && !s.includes('.')
            );
            potentialSkills.forEach(skill => this.addSkill(skill));
            if (potentialSkills.length > 0) foundUsefulContent = true;
        }

        if (foundUsefulContent) {
            this.updatePreview();
            this.showToast('Partial optimization applied - some suggestions extracted', 'warning');
        } else {
            this.showToast('Could not parse optimization response. Please try again.', 'error');
        }
    }

    // Utility Functions
    showLoading(message = 'Processing...') {
        const overlay = document.getElementById('loadingOverlay');
        const messageEl = overlay.querySelector('p');
        messageEl.textContent = message;
        overlay.style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    showToast(message, type = 'success') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const iconMap = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };

        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-${iconMap[type] || 'info-circle'}"></i>
                ${message}
            </div>
        `;

        container.appendChild(toast);

        // Longer duration for info messages
        const duration = type === 'info' ? 8000 : 5000;
        setTimeout(() => {
            toast.remove();
        }, duration);
    }

    updatePreview() {
        const previewContainer = document.getElementById('resumePreview');

        if (this.isDataEmpty()) {
            previewContainer.innerHTML = `
                <div class="preview-placeholder">
                    <i class="fas fa-file-alt"></i>
                    <h3>Your Resume Preview</h3>
                    <p>Start filling in your information to see your resume come to life!</p>
                </div>
            `;
            return;
        }

        const template = this.getTemplate(this.currentTemplate);
        previewContainer.innerHTML = template;
    }

    isDataEmpty() {
        const hasPersonalInfo = Object.values(this.data.personal).some(value => value.trim());
        const hasSummary = this.data.summary.trim();
        const hasExperience = this.data.experience.some(exp => exp.title || exp.company);
        const hasEducation = this.data.education.some(edu => edu.degree || edu.school);
        const hasSkills = this.data.skills.length > 0;

        return !hasPersonalInfo && !hasSummary && !hasExperience && !hasEducation && !hasSkills;
    }

    getTemplate(templateName) {
        switch (templateName) {
            case 'modern':
                return this.getModernTemplate();
            case 'classic':
                return this.getClassicTemplate();
            case 'minimal':
                return this.getMinimalTemplate();
            case 'creative':
                return this.getCreativeTemplate();
            default:
                return this.getModernTemplate();
        }
    }

    getModernTemplate() {
        return `
            <div class="resume modern-template" style="padding: 40px; font-family: 'Inter', sans-serif;">
                ${this.renderHeader('modern')}
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 40px; margin-top: 30px;">
                    <div class="left-column">
                        ${this.renderExperienceSection('modern')}
                        ${this.renderEducationSection('modern')}
                    </div>
                    <div class="right-column">
                        ${this.renderSummarySection('modern')}
                        ${this.renderSkillsSection('modern')}
                    </div>
                </div>
            </div>
        `;
    }

    getClassicTemplate() {
        return `
            <div class="resume classic-template" style="padding: 40px; font-family: 'Times New Roman', serif;">
                ${this.renderHeader('classic')}
                <div style="margin-top: 30px;">
                    ${this.renderSummarySection('classic')}
                    ${this.renderExperienceSection('classic')}
                    ${this.renderEducationSection('classic')}
                    ${this.renderSkillsSection('classic')}
                </div>
            </div>
        `;
    }

    getMinimalTemplate() {
        return `
            <div class="resume minimal-template" style="padding: 40px; font-family: 'Inter', sans-serif; color: #333;">
                ${this.renderHeader('minimal')}
                <div style="margin-top: 30px;">
                    ${this.renderSummarySection('minimal')}
                    ${this.renderExperienceSection('minimal')}
                    ${this.renderEducationSection('minimal')}
                    ${this.renderSkillsSection('minimal')}
                </div>
            </div>
        `;
    }

    getCreativeTemplate() {
        return `
            <div class="resume creative-template" style="padding: 40px; font-family: 'Inter', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                ${this.renderHeader('creative')}
                <div style="margin-top: 30px; background: white; color: #333; padding: 30px; border-radius: 10px;">
                    ${this.renderSummarySection('creative')}
                    ${this.renderExperienceSection('creative')}
                    ${this.renderEducationSection('creative')}
                    ${this.renderSkillsSection('creative')}
                </div>
            </div>
        `;
    }

    renderHeader(template) {
        const personal = this.data.personal;
        const styles = this.getTemplateStyles(template);

        return `
            <header style="${styles.header}">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <div>
                        <h1 style="${styles.name}">${personal.fullName || 'Your Name'}</h1>
                        <h2 style="${styles.title}">${personal.jobTitle || 'Your Job Title'}</h2>
                        <div style="margin-top: 15px;">
                            ${personal.email ? `<div style="${styles.contact}">📧 ${personal.email}</div>` : ''}
                            ${personal.phone ? `<div style="${styles.contact}">📞 ${personal.phone}</div>` : ''}
                            ${personal.location ? `<div style="${styles.contact}">📍 ${personal.location}</div>` : ''}
                            ${personal.linkedin ? `<div style="${styles.contact}">🔗 ${personal.linkedin}</div>` : ''}
                            ${personal.website ? `<div style="${styles.contact}">🌐 ${personal.website}</div>` : ''}
                        </div>
                    </div>
                </div>
            </header>
        `;
    }

    renderSummarySection(template) {
        if (!this.data.summary.trim()) return '';

        const styles = this.getTemplateStyles(template);
        return `
            <section style="margin-bottom: 30px;">
                <h3 style="${styles.sectionTitle}">PROFESSIONAL SUMMARY</h3>
                <p style="${styles.text}">${this.data.summary}</p>
            </section>
        `;
    }

    renderExperienceSection(template) {
        if (this.data.experience.length === 0 || !this.data.experience.some(exp => exp.title || exp.company)) {
            return '';
        }

        const styles = this.getTemplateStyles(template);
        return `
            <section style="margin-bottom: 30px;">
                <h3 style="${styles.sectionTitle}">EXPERIENCE</h3>
                ${this.data.experience.map(exp => {
                    if (!exp.title && !exp.company) return '';

                    const dateRange = this.formatDateRange(exp.startDate, exp.endDate, exp.current);
                    return `
                        <div style="margin-bottom: 25px;">
                            <h4 style="${styles.jobTitle}">${exp.title || 'Job Title'}</h4>
                            <div style="${styles.company}">${exp.company || 'Company Name'}</div>
                            <div style="${styles.jobMeta}">
                                ${dateRange ? `<span>${dateRange}</span>` : ''}
                                ${exp.location ? `<span> • ${exp.location}</span>` : ''}
                            </div>
                            ${exp.description ? `<p style="${styles.text}">${exp.description}</p>` : ''}
                        </div>
                    `;
                }).join('')}
            </section>
        `;
    }

    renderEducationSection(template) {
        if (this.data.education.length === 0 || !this.data.education.some(edu => edu.degree || edu.school)) {
            return '';
        }

        const styles = this.getTemplateStyles(template);
        return `
            <section style="margin-bottom: 30px;">
                <h3 style="${styles.sectionTitle}">EDUCATION</h3>
                ${this.data.education.map(edu => {
                    if (!edu.degree && !edu.school) return '';

                    return `
                        <div style="margin-bottom: 20px;">
                            <h4 style="${styles.jobTitle}">${edu.degree || 'Degree'}</h4>
                            <div style="${styles.company}">${edu.school || 'School Name'}</div>
                            <div style="${styles.jobMeta}">
                                ${edu.graduationDate ? `<span>${this.formatDate(edu.graduationDate)}</span>` : ''}
                                ${edu.location ? `<span> • ${edu.location}</span>` : ''}
                                ${edu.gpa ? `<span> • GPA: ${edu.gpa}</span>` : ''}
                            </div>
                            ${edu.description ? `<p style="${styles.text}">${edu.description}</p>` : ''}
                        </div>
                    `;
                }).join('')}
            </section>
        `;
    }

    renderSkillsSection(template) {
        if (this.data.skills.length === 0) return '';

        const styles = this.getTemplateStyles(template);
        return `
            <section style="margin-bottom: 30px;">
                <h3 style="${styles.sectionTitle}">SKILLS</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                    ${this.data.skills.map(skill => `
                        <span style="${styles.skillTag}">${skill}</span>
                    `).join('')}
                </div>
            </section>
        `;
    }

    getTemplateStyles(template) {
        const baseStyles = {
            modern: {
                header: 'margin-bottom: 30px;',
                name: 'font-size: 2.5rem; font-weight: 700; color: #000; margin-bottom: 5px; letter-spacing: 1px;',
                title: 'font-size: 1.2rem; color: #3b82f6; font-weight: 500; margin-bottom: 20px;',
                contact: 'margin-bottom: 5px; color: #6b7280;',
                sectionTitle: 'font-size: 1.1rem; font-weight: 600; color: #000; margin-bottom: 15px; padding-bottom: 5px; border-bottom: 2px solid #000; text-transform: uppercase; letter-spacing: 0.5px;',
                jobTitle: 'font-size: 1.1rem; font-weight: 600; color: #000; margin-bottom: 5px;',
                company: 'color: #3b82f6; font-weight: 500; margin-bottom: 5px;',
                jobMeta: 'color: #6b7280; font-size: 0.9rem; margin-bottom: 10px;',
                text: 'color: #374151; line-height: 1.6; margin-bottom: 10px;',
                skillTag: 'background-color: #eff6ff; color: #1d4ed8; padding: 4px 12px; border-radius: 15px; font-size: 0.8rem;'
            },
            classic: {
                header: 'margin-bottom: 30px; text-align: center; border-bottom: 2px solid #000; padding-bottom: 20px;',
                name: 'font-size: 2.2rem; font-weight: bold; color: #000; margin-bottom: 5px;',
                title: 'font-size: 1.1rem; color: #333; font-style: italic; margin-bottom: 15px;',
                contact: 'margin-bottom: 3px; color: #333; display: inline-block; margin-right: 20px;',
                sectionTitle: 'font-size: 1.2rem; font-weight: bold; color: #000; margin-bottom: 15px; text-transform: uppercase; border-bottom: 1px solid #000; padding-bottom: 3px;',
                jobTitle: 'font-size: 1.1rem; font-weight: bold; color: #000; margin-bottom: 3px;',
                company: 'color: #333; font-style: italic; margin-bottom: 3px;',
                jobMeta: 'color: #666; font-size: 0.9rem; margin-bottom: 8px;',
                text: 'color: #333; line-height: 1.6; margin-bottom: 8px;',
                skillTag: 'background-color: #f5f5f5; color: #333; padding: 3px 8px; border: 1px solid #ccc; font-size: 0.8rem; margin-right: 5px; margin-bottom: 5px; display: inline-block;'
            },
            minimal: {
                header: 'margin-bottom: 40px;',
                name: 'font-size: 2rem; font-weight: 300; color: #000; margin-bottom: 5px;',
                title: 'font-size: 1rem; color: #666; font-weight: 400; margin-bottom: 20px;',
                contact: 'margin-bottom: 5px; color: #666; font-size: 0.9rem;',
                sectionTitle: 'font-size: 1rem; font-weight: 500; color: #000; margin-bottom: 20px; text-transform: uppercase; letter-spacing: 2px;',
                jobTitle: 'font-size: 1rem; font-weight: 500; color: #000; margin-bottom: 3px;',
                company: 'color: #666; font-weight: 400; margin-bottom: 3px;',
                jobMeta: 'color: #999; font-size: 0.8rem; margin-bottom: 10px;',
                text: 'color: #555; line-height: 1.7; margin-bottom: 10px; font-size: 0.9rem;',
                skillTag: 'color: #333; padding: 2px 0; border-bottom: 1px solid #eee; font-size: 0.8rem; margin-right: 15px; display: inline-block;'
            },
            creative: {
                header: 'margin-bottom: 30px; text-align: center;',
                name: 'font-size: 2.5rem; font-weight: 700; color: white; margin-bottom: 5px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);',
                title: 'font-size: 1.2rem; color: rgba(255,255,255,0.9); font-weight: 400; margin-bottom: 20px;',
                contact: 'margin-bottom: 5px; color: rgba(255,255,255,0.8); display: inline-block; margin-right: 20px;',
                sectionTitle: 'font-size: 1.1rem; font-weight: 600; color: #667eea; margin-bottom: 15px; text-transform: uppercase; letter-spacing: 1px;',
                jobTitle: 'font-size: 1.1rem; font-weight: 600; color: #333; margin-bottom: 5px;',
                company: 'color: #667eea; font-weight: 500; margin-bottom: 5px;',
                jobMeta: 'color: #666; font-size: 0.9rem; margin-bottom: 10px;',
                text: 'color: #555; line-height: 1.6; margin-bottom: 10px;',
                skillTag: 'background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 6px 12px; border-radius: 20px; font-size: 0.8rem; margin-right: 8px; margin-bottom: 8px; display: inline-block;'
            }
        };

        return baseStyles[template] || baseStyles.modern;
    }

    formatDateRange(startDate, endDate, current) {
        if (!startDate) return '';

        const start = this.formatDate(startDate);
        const end = current ? 'Present' : (endDate ? this.formatDate(endDate) : '');

        return end ? `${start} - ${end}` : start;
    }

    formatDate(dateString) {
        if (!dateString) return '';

        const date = new Date(dateString + '-01'); // Add day for month input
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long'
        });
    }

    saveData() {
        localStorage.setItem('resumeBuilderData', JSON.stringify(this.data));
        this.showToast('Progress saved successfully!');
    }

    loadSavedData() {
        const saved = localStorage.getItem('resumeBuilderData');
        if (saved) {
            this.data = { ...this.data, ...JSON.parse(saved) };
            this.populateForm();
        }

        // Load saved API key
        const savedApiKey = localStorage.getItem('resumeBuilderApiKey');
        if (savedApiKey) {
            this.data.apiKey = savedApiKey;
            document.getElementById('apiKey').value = savedApiKey;
        }
    }

    populateForm() {
        // Populate personal info
        Object.keys(this.data.personal).forEach(key => {
            const element = document.getElementById(key);
            if (element) element.value = this.data.personal[key];
        });

        // Populate summary
        document.getElementById('summary').value = this.data.summary;

        // Populate skills
        this.renderSkills();

        // Populate experience and education
        this.renderExperience();
        this.renderEducation();
    }

    setupAutoSave() {
        setInterval(() => {
            this.saveData();
        }, 30000); // Auto-save every 30 seconds
    }

    exportPDF() {
        if (this.isDataEmpty()) {
            this.showToast('Please fill in some information before exporting', 'warning');
            return;
        }

        this.showLoading('Generating PDF...');

        const resumeElement = document.querySelector('#resumePreview .resume');
        if (!resumeElement) {
            this.hideLoading();
            this.showToast('No resume content to export', 'error');
            return;
        }

        // Ensure window is scrolled to top before PDF generation
        window.scrollTo(0, 0);

        const options = {
            margin: [0.2, 0.4, 0.4, 0.4],
            filename: `${this.data.personal.fullName || 'Resume'}_Resume.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 1.5,
                useCORS: true,
                letterRendering: true,
                allowTaint: false,
                removeContainer: true,
                imageTimeout: 15000,
                logging: false
            },
            jsPDF: {
                unit: 'in',
                format: 'letter',
                orientation: 'portrait',
                compress: true
            },
            pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
        };

        html2pdf()
            .set(options)
            .from(resumeElement)
            .save()
            .then(() => {
                this.hideLoading();
                this.showToast('PDF exported successfully!');
            })
            .catch((error) => {
                console.error('PDF export error:', error);
                this.hideLoading();
                this.showToast('Failed to export PDF. Please try again.', 'error');
            });
    }
}

// Initialize the app
let resumeBuilder;
document.addEventListener('DOMContentLoaded', () => {
    resumeBuilder = new ResumeBuilder();
});
