// AI Resume Builder - Main JavaScript File

class ResumeBuilder {
    constructor() {
        this.data = {
            personal: {},
            summary: '',
            experience: [],
            education: [],
            skills: [],
            apiKey: ''
        };

        this.currentTemplate = 'modern';
        this.init();
    }

    init() {
        this.loadSavedData();
        this.bindEvents();
        this.addInitialExperience();
        this.addInitialEducation();
    }

    bindEvents() {
        // API Key toggle
        document.getElementById('toggleApiKey').addEventListener('click', this.toggleApiKeyVisibility);

        // Form inputs
        this.bindPersonalInfoEvents();
        this.bindSummaryEvents();
        this.bindSkillsEvents();
        this.bindExperienceEvents();
        this.bindEducationEvents();

        // AI features
        document.getElementById('generateSummary').addEventListener('click', () => this.generateSummary());
        document.getElementById('suggestSkills').addEventListener('click', () => this.suggestSkills());
        document.getElementById('optimizeResume').addEventListener('click', () => this.optimizeResume());

        // Template selection
        document.getElementById('templateSelect').addEventListener('change', (e) => {
            this.currentTemplate = e.target.value;
            this.updatePreview();
        });

        // Save and export
        document.getElementById('saveBtn').addEventListener('click', () => this.saveData());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportPDF());

        // Auto-save on input changes
        this.setupAutoSave();
    }

    bindPersonalInfoEvents() {
        const personalFields = ['fullName', 'jobTitle', 'email', 'phone', 'location', 'linkedin', 'website'];
        personalFields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                element.addEventListener('input', (e) => {
                    this.data.personal[field] = e.target.value;
                    this.updatePreview();
                });
            }
        });
    }

    bindSummaryEvents() {
        document.getElementById('summary').addEventListener('input', (e) => {
            this.data.summary = e.target.value;
            this.updatePreview();
        });
    }

    bindSkillsEvents() {
        const skillInput = document.getElementById('skillInput');
        skillInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.target.value.trim()) {
                this.addSkill(e.target.value.trim());
                e.target.value = '';
            }
        });
    }

    bindExperienceEvents() {
        document.getElementById('addExperience').addEventListener('click', () => this.addExperience());
    }

    bindEducationEvents() {
        document.getElementById('addEducation').addEventListener('click', () => this.addEducation());
    }

    toggleApiKeyVisibility() {
        const apiKeyInput = document.getElementById('apiKey');
        const toggleBtn = document.getElementById('toggleApiKey');
        const icon = toggleBtn.querySelector('i');

        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            apiKeyInput.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    addSkill(skillName) {
        if (!this.data.skills.includes(skillName)) {
            this.data.skills.push(skillName);
            this.renderSkills();
            this.updatePreview();
        }
    }

    removeSkill(skillName) {
        this.data.skills = this.data.skills.filter(skill => skill !== skillName);
        this.renderSkills();
        this.updatePreview();
    }

    renderSkills() {
        const container = document.getElementById('skillsContainer');
        container.innerHTML = this.data.skills.map(skill => `
            <div class="skill-tag">
                ${skill}
                <button class="remove-skill" onclick="resumeBuilder.removeSkill('${skill}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }

    addExperience() {
        const experience = {
            id: Date.now(),
            title: '',
            company: '',
            location: '',
            startDate: '',
            endDate: '',
            current: false,
            description: ''
        };

        this.data.experience.push(experience);
        this.renderExperience();
    }

    addInitialExperience() {
        if (this.data.experience.length === 0) {
            this.addExperience();
        } else {
            this.renderExperience();
        }
    }

    renderExperience() {
        const container = document.getElementById('experienceContainer');
        container.innerHTML = this.data.experience.map(exp => this.createExperienceHTML(exp)).join('');
    }

    createExperienceHTML(exp) {
        return `
            <div class="experience-item" data-id="${exp.id}">
                <div class="form-grid">
                    <input type="text" placeholder="Job Title" value="${exp.title}"
                           onchange="resumeBuilder.updateExperience(${exp.id}, 'title', this.value)">
                    <input type="text" placeholder="Company" value="${exp.company}"
                           onchange="resumeBuilder.updateExperience(${exp.id}, 'company', this.value)">
                    <input type="text" placeholder="Location" value="${exp.location}"
                           onchange="resumeBuilder.updateExperience(${exp.id}, 'location', this.value)">
                    <input type="month" placeholder="Start Date" value="${exp.startDate}"
                           onchange="resumeBuilder.updateExperience(${exp.id}, 'startDate', this.value)">
                    <input type="month" placeholder="End Date" value="${exp.endDate}"
                           ${exp.current ? 'disabled' : ''}
                           onchange="resumeBuilder.updateExperience(${exp.id}, 'endDate', this.value)">
                    <label>
                        <input type="checkbox" ${exp.current ? 'checked' : ''}
                               onchange="resumeBuilder.updateExperience(${exp.id}, 'current', this.checked)">
                        Current Position
                    </label>
                </div>
                <textarea placeholder="Job description and achievements..." rows="3"
                          onchange="resumeBuilder.updateExperience(${exp.id}, 'description', this.value)">${exp.description}</textarea>
                <div class="item-actions">
                    <button class="btn btn-ai" onclick="resumeBuilder.enhanceJobDescription(${exp.id})">
                        <i class="fas fa-magic"></i> Enhance with AI
                    </button>
                    <button class="btn btn-secondary" onclick="resumeBuilder.removeExperience(${exp.id})">
                        <i class="fas fa-trash"></i> Remove
                    </button>
                </div>
            </div>
        `;
    }

    updateExperience(id, field, value) {
        const exp = this.data.experience.find(e => e.id === id);
        if (exp) {
            exp[field] = value;
            if (field === 'current' && value) {
                exp.endDate = '';
                this.renderExperience();
            }
            this.updatePreview();
        }
    }

    removeExperience(id) {
        this.data.experience = this.data.experience.filter(e => e.id !== id);
        this.renderExperience();
        this.updatePreview();
    }

    addEducation() {
        const education = {
            id: Date.now(),
            degree: '',
            school: '',
            location: '',
            graduationDate: '',
            gpa: '',
            description: ''
        };

        this.data.education.push(education);
        this.renderEducation();
    }

    addInitialEducation() {
        if (this.data.education.length === 0) {
            this.addEducation();
        } else {
            this.renderEducation();
        }
    }

    renderEducation() {
        const container = document.getElementById('educationContainer');
        container.innerHTML = this.data.education.map(edu => this.createEducationHTML(edu)).join('');
    }

    createEducationHTML(edu) {
        return `
            <div class="education-item" data-id="${edu.id}">
                <div class="form-grid">
                    <input type="text" placeholder="Degree" value="${edu.degree}"
                           onchange="resumeBuilder.updateEducation(${edu.id}, 'degree', this.value)">
                    <input type="text" placeholder="School/University" value="${edu.school}"
                           onchange="resumeBuilder.updateEducation(${edu.id}, 'school', this.value)">
                    <input type="text" placeholder="Location" value="${edu.location}"
                           onchange="resumeBuilder.updateEducation(${edu.id}, 'location', this.value)">
                    <input type="month" placeholder="Graduation Date" value="${edu.graduationDate}"
                           onchange="resumeBuilder.updateEducation(${edu.id}, 'graduationDate', this.value)">
                    <input type="text" placeholder="GPA (optional)" value="${edu.gpa}"
                           onchange="resumeBuilder.updateEducation(${edu.id}, 'gpa', this.value)">
                </div>
                <textarea placeholder="Additional details, honors, relevant coursework..." rows="2"
                          onchange="resumeBuilder.updateEducation(${edu.id}, 'description', this.value)">${edu.description}</textarea>
                <button class="btn btn-secondary" onclick="resumeBuilder.removeEducation(${edu.id})">
                    <i class="fas fa-trash"></i> Remove
                </button>
            </div>
        `;
    }

    updateEducation(id, field, value) {
        const edu = this.data.education.find(e => e.id === id);
        if (edu) {
            edu[field] = value;
            this.updatePreview();
        }
    }

    removeEducation(id) {
        this.data.education = this.data.education.filter(e => e.id !== id);
        this.renderEducation();
        this.updatePreview();
    }

    // AI Functions
    async generateSummary() {
        const apiKey = document.getElementById('apiKey').value;
        if (!apiKey) {
            this.showToast('Please enter your OpenRouter API key first', 'error');
            return;
        }

        this.showLoading('Generating professional summary...');

        try {
            const prompt = this.buildSummaryPrompt();
            const response = await this.callOpenRouter(apiKey, prompt);

            if (response) {
                document.getElementById('summary').value = response;
                this.data.summary = response;
                this.updatePreview();
                this.showToast('Professional summary generated successfully!');
            }
        } catch (error) {
            console.error('Error generating summary:', error);
            this.showToast('Failed to generate summary. Please check your API key.', 'error');
        }

        this.hideLoading();
    }

    async suggestSkills() {
        const apiKey = document.getElementById('apiKey').value;
        if (!apiKey) {
            this.showToast('Please enter your OpenRouter API key first', 'error');
            return;
        }

        this.showLoading('Suggesting relevant skills...');

        try {
            const prompt = this.buildSkillsPrompt();
            const response = await this.callOpenRouter(apiKey, prompt);

            if (response) {
                const skills = response.split(',').map(s => s.trim()).filter(s => s);
                skills.forEach(skill => this.addSkill(skill));
                this.showToast(`Added ${skills.length} skill suggestions!`);
            }
        } catch (error) {
            console.error('Error suggesting skills:', error);
            this.showToast('Failed to suggest skills. Please check your API key.', 'error');
        }

        this.hideLoading();
    }

    async optimizeResume() {
        const apiKey = document.getElementById('apiKey').value;
        const jobDescription = document.getElementById('jobDescription').value;

        if (!apiKey) {
            this.showToast('Please enter your OpenRouter API key first', 'error');
            return;
        }

        if (!jobDescription.trim()) {
            this.showToast('Please paste a job description first', 'error');
            return;
        }

        this.showLoading('Optimizing resume for job description...');

        try {
            const prompt = this.buildOptimizationPrompt(jobDescription);
            const response = await this.callOpenRouter(apiKey, prompt);

            if (response) {
                // Parse the AI response and update relevant sections
                this.parseOptimizationResponse(response);
                this.showToast('Resume optimized for the job description!');
            }
        } catch (error) {
            console.error('Error optimizing resume:', error);
            this.showToast('Failed to optimize resume. Please check your API key.', 'error');
        }

        this.hideLoading();
    }

    async enhanceJobDescription(id) {
        const apiKey = document.getElementById('apiKey').value;
        if (!apiKey) {
            this.showToast('Please enter your OpenRouter API key first', 'error');
            return;
        }

        const experience = this.data.experience.find(e => e.id === id);
        if (!experience || !experience.description.trim()) {
            this.showToast('Please add a job description first', 'error');
            return;
        }

        this.showLoading('Enhancing job description...');

        try {
            const prompt = this.buildJobEnhancementPrompt(experience);
            const response = await this.callOpenRouter(apiKey, prompt);

            if (response) {
                experience.description = response;
                this.renderExperience();
                this.updatePreview();
                this.showToast('Job description enhanced successfully!');
            }
        } catch (error) {
            console.error('Error enhancing job description:', error);
            this.showToast('Failed to enhance job description. Please check your API key.', 'error');
        }

        this.hideLoading();
    }

    async callOpenRouter(apiKey, prompt) {
        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': window.location.origin,
                'X-Title': 'AI Resume Builder'
            },
            body: JSON.stringify({
                model: 'anthropic/claude-3.5-sonnet',
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 1000,
                temperature: 0.7
            })
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
        }

        const data = await response.json();
        return data.choices[0]?.message?.content?.trim();
    }

    buildSummaryPrompt() {
        const personal = this.data.personal;
        const experience = this.data.experience;

        return `Create a professional summary for a resume based on the following information:

Name: ${personal.fullName || 'Not provided'}
Job Title: ${personal.jobTitle || 'Not provided'}
Experience: ${experience.map(exp => `${exp.title} at ${exp.company}`).join(', ') || 'Not provided'}

Write a compelling 2-3 sentence professional summary that highlights key strengths, experience, and value proposition. Make it specific and impactful. Return only the summary text without any additional formatting or explanations.`;
    }

    buildSkillsPrompt() {
        const personal = this.data.personal;
        const experience = this.data.experience;

        return `Suggest 8-10 relevant professional skills for someone with this background:

Job Title: ${personal.jobTitle || 'Not provided'}
Experience: ${experience.map(exp => `${exp.title} at ${exp.company}: ${exp.description}`).join('\n') || 'Not provided'}

Return only a comma-separated list of skills, no explanations or additional text. Focus on technical skills, soft skills, and industry-specific competencies.`;
    }

    buildOptimizationPrompt(jobDescription) {
        return `Analyze this job description and suggest improvements to make the resume more relevant:

Job Description:
${jobDescription}

Current Resume Data:
${JSON.stringify(this.data, null, 2)}

Provide specific suggestions for:
1. Updated professional summary
2. Additional relevant skills
3. Enhanced job descriptions

Format your response as JSON with keys: summary, skills, experienceEnhancements`;
    }

    buildJobEnhancementPrompt(experience) {
        return `Enhance this job description to be more impactful and achievement-focused:

Job Title: ${experience.title}
Company: ${experience.company}
Current Description: ${experience.description}

Rewrite the description to:
- Use strong action verbs
- Include quantifiable achievements where possible
- Highlight relevant skills and technologies
- Make it more compelling to employers

Return only the enhanced description, no additional formatting or explanations.`;
    }

    parseOptimizationResponse(response) {
        try {
            const optimization = JSON.parse(response);

            if (optimization.summary) {
                document.getElementById('summary').value = optimization.summary;
                this.data.summary = optimization.summary;
            }

            if (optimization.skills && Array.isArray(optimization.skills)) {
                optimization.skills.forEach(skill => this.addSkill(skill));
            }

            this.updatePreview();
        } catch (error) {
            console.error('Error parsing optimization response:', error);
            this.showToast('Received optimization suggestions but could not parse them', 'warning');
        }
    }

    // Utility Functions
    showLoading(message = 'Processing...') {
        const overlay = document.getElementById('loadingOverlay');
        const messageEl = overlay.querySelector('p');
        messageEl.textContent = message;
        overlay.style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    showToast(message, type = 'success') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'exclamation-triangle'}"></i>
                ${message}
            </div>
        `;

        container.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    updatePreview() {
        const previewContainer = document.getElementById('resumePreview');

        if (this.isDataEmpty()) {
            previewContainer.innerHTML = `
                <div class="preview-placeholder">
                    <i class="fas fa-file-alt"></i>
                    <h3>Your Resume Preview</h3>
                    <p>Start filling in your information to see your resume come to life!</p>
                </div>
            `;
            return;
        }

        const template = this.getTemplate(this.currentTemplate);
        previewContainer.innerHTML = template;
    }

    isDataEmpty() {
        const hasPersonalInfo = Object.values(this.data.personal).some(value => value.trim());
        const hasSummary = this.data.summary.trim();
        const hasExperience = this.data.experience.some(exp => exp.title || exp.company);
        const hasEducation = this.data.education.some(edu => edu.degree || edu.school);
        const hasSkills = this.data.skills.length > 0;

        return !hasPersonalInfo && !hasSummary && !hasExperience && !hasEducation && !hasSkills;
    }

    getTemplate(templateName) {
        switch (templateName) {
            case 'modern':
                return this.getModernTemplate();
            case 'classic':
                return this.getClassicTemplate();
            case 'minimal':
                return this.getMinimalTemplate();
            case 'creative':
                return this.getCreativeTemplate();
            default:
                return this.getModernTemplate();
        }
    }

    getModernTemplate() {
        return `
            <div class="resume modern-template" style="padding: 40px; font-family: 'Inter', sans-serif;">
                ${this.renderHeader('modern')}
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 40px; margin-top: 30px;">
                    <div class="left-column">
                        ${this.renderExperienceSection('modern')}
                        ${this.renderEducationSection('modern')}
                    </div>
                    <div class="right-column">
                        ${this.renderSummarySection('modern')}
                        ${this.renderSkillsSection('modern')}
                    </div>
                </div>
            </div>
        `;
    }

    getClassicTemplate() {
        return `
            <div class="resume classic-template" style="padding: 40px; font-family: 'Times New Roman', serif;">
                ${this.renderHeader('classic')}
                <div style="margin-top: 30px;">
                    ${this.renderSummarySection('classic')}
                    ${this.renderExperienceSection('classic')}
                    ${this.renderEducationSection('classic')}
                    ${this.renderSkillsSection('classic')}
                </div>
            </div>
        `;
    }

    getMinimalTemplate() {
        return `
            <div class="resume minimal-template" style="padding: 40px; font-family: 'Inter', sans-serif; color: #333;">
                ${this.renderHeader('minimal')}
                <div style="margin-top: 30px;">
                    ${this.renderSummarySection('minimal')}
                    ${this.renderExperienceSection('minimal')}
                    ${this.renderEducationSection('minimal')}
                    ${this.renderSkillsSection('minimal')}
                </div>
            </div>
        `;
    }

    getCreativeTemplate() {
        return `
            <div class="resume creative-template" style="padding: 40px; font-family: 'Inter', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                ${this.renderHeader('creative')}
                <div style="margin-top: 30px; background: white; color: #333; padding: 30px; border-radius: 10px;">
                    ${this.renderSummarySection('creative')}
                    ${this.renderExperienceSection('creative')}
                    ${this.renderEducationSection('creative')}
                    ${this.renderSkillsSection('creative')}
                </div>
            </div>
        `;
    }

    renderHeader(template) {
        const personal = this.data.personal;
        const styles = this.getTemplateStyles(template);

        return `
            <header style="${styles.header}">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <div>
                        <h1 style="${styles.name}">${personal.fullName || 'Your Name'}</h1>
                        <h2 style="${styles.title}">${personal.jobTitle || 'Your Job Title'}</h2>
                        <div style="margin-top: 15px;">
                            ${personal.email ? `<div style="${styles.contact}">📧 ${personal.email}</div>` : ''}
                            ${personal.phone ? `<div style="${styles.contact}">📞 ${personal.phone}</div>` : ''}
                            ${personal.location ? `<div style="${styles.contact}">📍 ${personal.location}</div>` : ''}
                            ${personal.linkedin ? `<div style="${styles.contact}">🔗 ${personal.linkedin}</div>` : ''}
                            ${personal.website ? `<div style="${styles.contact}">🌐 ${personal.website}</div>` : ''}
                        </div>
                    </div>
                </div>
            </header>
        `;
    }

    renderSummarySection(template) {
        if (!this.data.summary.trim()) return '';

        const styles = this.getTemplateStyles(template);
        return `
            <section style="margin-bottom: 30px;">
                <h3 style="${styles.sectionTitle}">PROFESSIONAL SUMMARY</h3>
                <p style="${styles.text}">${this.data.summary}</p>
            </section>
        `;
    }

    renderExperienceSection(template) {
        if (this.data.experience.length === 0 || !this.data.experience.some(exp => exp.title || exp.company)) {
            return '';
        }

        const styles = this.getTemplateStyles(template);
        return `
            <section style="margin-bottom: 30px;">
                <h3 style="${styles.sectionTitle}">EXPERIENCE</h3>
                ${this.data.experience.map(exp => {
                    if (!exp.title && !exp.company) return '';

                    const dateRange = this.formatDateRange(exp.startDate, exp.endDate, exp.current);
                    return `
                        <div style="margin-bottom: 25px;">
                            <h4 style="${styles.jobTitle}">${exp.title || 'Job Title'}</h4>
                            <div style="${styles.company}">${exp.company || 'Company Name'}</div>
                            <div style="${styles.jobMeta}">
                                ${dateRange ? `<span>${dateRange}</span>` : ''}
                                ${exp.location ? `<span> • ${exp.location}</span>` : ''}
                            </div>
                            ${exp.description ? `<p style="${styles.text}">${exp.description}</p>` : ''}
                        </div>
                    `;
                }).join('')}
            </section>
        `;
    }

    renderEducationSection(template) {
        if (this.data.education.length === 0 || !this.data.education.some(edu => edu.degree || edu.school)) {
            return '';
        }

        const styles = this.getTemplateStyles(template);
        return `
            <section style="margin-bottom: 30px;">
                <h3 style="${styles.sectionTitle}">EDUCATION</h3>
                ${this.data.education.map(edu => {
                    if (!edu.degree && !edu.school) return '';

                    return `
                        <div style="margin-bottom: 20px;">
                            <h4 style="${styles.jobTitle}">${edu.degree || 'Degree'}</h4>
                            <div style="${styles.company}">${edu.school || 'School Name'}</div>
                            <div style="${styles.jobMeta}">
                                ${edu.graduationDate ? `<span>${this.formatDate(edu.graduationDate)}</span>` : ''}
                                ${edu.location ? `<span> • ${edu.location}</span>` : ''}
                                ${edu.gpa ? `<span> • GPA: ${edu.gpa}</span>` : ''}
                            </div>
                            ${edu.description ? `<p style="${styles.text}">${edu.description}</p>` : ''}
                        </div>
                    `;
                }).join('')}
            </section>
        `;
    }

    renderSkillsSection(template) {
        if (this.data.skills.length === 0) return '';

        const styles = this.getTemplateStyles(template);
        return `
            <section style="margin-bottom: 30px;">
                <h3 style="${styles.sectionTitle}">SKILLS</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                    ${this.data.skills.map(skill => `
                        <span style="${styles.skillTag}">${skill}</span>
                    `).join('')}
                </div>
            </section>
        `;
    }

    getTemplateStyles(template) {
        const baseStyles = {
            modern: {
                header: 'margin-bottom: 30px;',
                name: 'font-size: 2.5rem; font-weight: 700; color: #000; margin-bottom: 5px; letter-spacing: 1px;',
                title: 'font-size: 1.2rem; color: #3b82f6; font-weight: 500; margin-bottom: 20px;',
                contact: 'margin-bottom: 5px; color: #6b7280;',
                sectionTitle: 'font-size: 1.1rem; font-weight: 600; color: #000; margin-bottom: 15px; padding-bottom: 5px; border-bottom: 2px solid #000; text-transform: uppercase; letter-spacing: 0.5px;',
                jobTitle: 'font-size: 1.1rem; font-weight: 600; color: #000; margin-bottom: 5px;',
                company: 'color: #3b82f6; font-weight: 500; margin-bottom: 5px;',
                jobMeta: 'color: #6b7280; font-size: 0.9rem; margin-bottom: 10px;',
                text: 'color: #374151; line-height: 1.6; margin-bottom: 10px;',
                skillTag: 'background-color: #eff6ff; color: #1d4ed8; padding: 4px 12px; border-radius: 15px; font-size: 0.8rem;'
            },
            classic: {
                header: 'margin-bottom: 30px; text-align: center; border-bottom: 2px solid #000; padding-bottom: 20px;',
                name: 'font-size: 2.2rem; font-weight: bold; color: #000; margin-bottom: 5px;',
                title: 'font-size: 1.1rem; color: #333; font-style: italic; margin-bottom: 15px;',
                contact: 'margin-bottom: 3px; color: #333; display: inline-block; margin-right: 20px;',
                sectionTitle: 'font-size: 1.2rem; font-weight: bold; color: #000; margin-bottom: 15px; text-transform: uppercase; border-bottom: 1px solid #000; padding-bottom: 3px;',
                jobTitle: 'font-size: 1.1rem; font-weight: bold; color: #000; margin-bottom: 3px;',
                company: 'color: #333; font-style: italic; margin-bottom: 3px;',
                jobMeta: 'color: #666; font-size: 0.9rem; margin-bottom: 8px;',
                text: 'color: #333; line-height: 1.6; margin-bottom: 8px;',
                skillTag: 'background-color: #f5f5f5; color: #333; padding: 3px 8px; border: 1px solid #ccc; font-size: 0.8rem; margin-right: 5px; margin-bottom: 5px; display: inline-block;'
            },
            minimal: {
                header: 'margin-bottom: 40px;',
                name: 'font-size: 2rem; font-weight: 300; color: #000; margin-bottom: 5px;',
                title: 'font-size: 1rem; color: #666; font-weight: 400; margin-bottom: 20px;',
                contact: 'margin-bottom: 5px; color: #666; font-size: 0.9rem;',
                sectionTitle: 'font-size: 1rem; font-weight: 500; color: #000; margin-bottom: 20px; text-transform: uppercase; letter-spacing: 2px;',
                jobTitle: 'font-size: 1rem; font-weight: 500; color: #000; margin-bottom: 3px;',
                company: 'color: #666; font-weight: 400; margin-bottom: 3px;',
                jobMeta: 'color: #999; font-size: 0.8rem; margin-bottom: 10px;',
                text: 'color: #555; line-height: 1.7; margin-bottom: 10px; font-size: 0.9rem;',
                skillTag: 'color: #333; padding: 2px 0; border-bottom: 1px solid #eee; font-size: 0.8rem; margin-right: 15px; display: inline-block;'
            },
            creative: {
                header: 'margin-bottom: 30px; text-align: center;',
                name: 'font-size: 2.5rem; font-weight: 700; color: white; margin-bottom: 5px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);',
                title: 'font-size: 1.2rem; color: rgba(255,255,255,0.9); font-weight: 400; margin-bottom: 20px;',
                contact: 'margin-bottom: 5px; color: rgba(255,255,255,0.8); display: inline-block; margin-right: 20px;',
                sectionTitle: 'font-size: 1.1rem; font-weight: 600; color: #667eea; margin-bottom: 15px; text-transform: uppercase; letter-spacing: 1px;',
                jobTitle: 'font-size: 1.1rem; font-weight: 600; color: #333; margin-bottom: 5px;',
                company: 'color: #667eea; font-weight: 500; margin-bottom: 5px;',
                jobMeta: 'color: #666; font-size: 0.9rem; margin-bottom: 10px;',
                text: 'color: #555; line-height: 1.6; margin-bottom: 10px;',
                skillTag: 'background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 6px 12px; border-radius: 20px; font-size: 0.8rem; margin-right: 8px; margin-bottom: 8px; display: inline-block;'
            }
        };

        return baseStyles[template] || baseStyles.modern;
    }

    formatDateRange(startDate, endDate, current) {
        if (!startDate) return '';

        const start = this.formatDate(startDate);
        const end = current ? 'Present' : (endDate ? this.formatDate(endDate) : '');

        return end ? `${start} - ${end}` : start;
    }

    formatDate(dateString) {
        if (!dateString) return '';

        const date = new Date(dateString + '-01'); // Add day for month input
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long'
        });
    }

    saveData() {
        localStorage.setItem('resumeBuilderData', JSON.stringify(this.data));
        this.showToast('Progress saved successfully!');
    }

    loadSavedData() {
        const saved = localStorage.getItem('resumeBuilderData');
        if (saved) {
            this.data = { ...this.data, ...JSON.parse(saved) };
            this.populateForm();
        }
    }

    populateForm() {
        // Populate personal info
        Object.keys(this.data.personal).forEach(key => {
            const element = document.getElementById(key);
            if (element) element.value = this.data.personal[key];
        });

        // Populate summary
        document.getElementById('summary').value = this.data.summary;

        // Populate skills
        this.renderSkills();

        // Populate experience and education
        this.renderExperience();
        this.renderEducation();
    }

    setupAutoSave() {
        setInterval(() => {
            this.saveData();
        }, 30000); // Auto-save every 30 seconds
    }

    exportPDF() {
        if (this.isDataEmpty()) {
            this.showToast('Please fill in some information before exporting', 'warning');
            return;
        }

        this.showLoading('Generating PDF...');

        const resumeElement = document.querySelector('#resumePreview .resume');
        if (!resumeElement) {
            this.hideLoading();
            this.showToast('No resume content to export', 'error');
            return;
        }

        // Ensure window is scrolled to top before PDF generation
        window.scrollTo(0, 0);

        const options = {
            margin: [0.2, 0.4, 0.4, 0.4],
            filename: `${this.data.personal.fullName || 'Resume'}_Resume.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 1.5,
                useCORS: true,
                letterRendering: true,
                allowTaint: false,
                removeContainer: true,
                imageTimeout: 15000,
                logging: false
            },
            jsPDF: {
                unit: 'in',
                format: 'letter',
                orientation: 'portrait',
                compress: true
            },
            pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
        };

        html2pdf()
            .set(options)
            .from(resumeElement)
            .save()
            .then(() => {
                this.hideLoading();
                this.showToast('PDF exported successfully!');
            })
            .catch((error) => {
                console.error('PDF export error:', error);
                this.hideLoading();
                this.showToast('Failed to export PDF. Please try again.', 'error');
            });
    }
}

// Initialize the app
let resumeBuilder;
document.addEventListener('DOMContentLoaded', () => {
    resumeBuilder = new ResumeBuilder();
});
