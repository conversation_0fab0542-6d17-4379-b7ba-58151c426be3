document.addEventListener('DOMContentLoaded', function() {
    const downloadBtn = document.getElementById('downloadBtn');
    const profilePicture = document.getElementById('profilePicture');
    const imageUpload = document.getElementById('imageUpload');
    const profileImage = document.getElementById('profileImage');
    const avatarPlaceholder = document.getElementById('avatarPlaceholder');
    const uploadOverlay = document.getElementById('uploadOverlay');

    downloadBtn.addEventListener('click', function() {
        // Show loading state
        downloadBtn.textContent = 'Generating PDF...';
        downloadBtn.disabled = true;

        // Get the resume element
        const resume = document.getElementById('resume');

        // Fix for content-length related positioning issues
        const container = document.querySelector('.container');
        const originalContainerStyle = container.style.cssText;

        // Apply comprehensive fixes for long content
        container.style.margin = '0 auto';
        container.style.padding = '0';
        container.style.boxShadow = 'none';

        // Force scroll to top and wait a moment for layout to settle
        window.scrollTo(0, 0);

        // PDF generation with settings optimized for long content
        const options = {
            margin: [0.2, 0.4, 0.4, 0.4], // Minimal top margin
            filename: 'Jalan_McRae_Resume.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 1.5, // Slightly lower scale for better handling of complex content
                useCORS: true,
                letterRendering: true,
                allowTaint: false,
                removeContainer: true, // Remove container after rendering
                imageTimeout: 15000, // Longer timeout for complex content
                logging: false
            },
            jsPDF: {
                unit: 'in',
                format: 'letter',
                orientation: 'portrait',
                compress: true
            },
            pagebreak: { mode: ['avoid-all', 'css', 'legacy'] } // Better page break handling
        };

        // Generate PDF
        html2pdf()
            .set(options)
            .from(resume)
            .save()
            .then(function() {
                // Restore original container style
                container.style.cssText = originalContainerStyle;
                // Reset button state
                downloadBtn.textContent = 'Download PDF';
                downloadBtn.disabled = false;
            })
            .catch(function(error) {
                console.error('Error generating PDF:', error);
                // Restore original container style even on error
                container.style.cssText = originalContainerStyle;
                downloadBtn.textContent = 'Download PDF';
                downloadBtn.disabled = false;
                alert('Error generating PDF. Please try again.');
            });
    });

    // Image upload functionality
    profilePicture.addEventListener('click', function() {
        imageUpload.click();
    });

    imageUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Please select an image smaller than 5MB.');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                profileImage.src = e.target.result;
                profileImage.style.display = 'block';
                avatarPlaceholder.style.display = 'none';
                uploadOverlay.style.display = 'none';

                // Store image in localStorage for persistence
                localStorage.setItem('resumeProfileImage', e.target.result);
            };
            reader.readAsDataURL(file);
        }
    });

    // Load saved image on page load
    const savedImage = localStorage.getItem('resumeProfileImage');
    if (savedImage) {
        profileImage.src = savedImage;
        profileImage.style.display = 'block';
        avatarPlaceholder.style.display = 'none';
        uploadOverlay.style.display = 'none';
    }
});

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});

// Add print functionality as backup
function printResume() {
    window.print();
}

// Keyboard shortcut for download (Ctrl+P)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        document.getElementById('downloadBtn').click();
    }
});
