document.addEventListener('DOMContentLoaded', function() {
    const downloadBtn = document.getElementById('downloadBtn');

    downloadBtn.addEventListener('click', function() {
        // Show loading state
        downloadBtn.textContent = 'Generating PDF...';
        downloadBtn.disabled = true;

        // Get the resume element
        const resume = document.getElementById('resume');

        // Simple PDF generation with minimal styling
        const options = {
            margin: [0.3, 0.5, 0.5, 0.5],
            filename: 'Jalan_McRae_Resume.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 2,
                useCORS: true,
                letterRendering: true
            },
            jsPDF: {
                unit: 'in',
                format: 'letter',
                orientation: 'portrait'
            }
        };

        // Generate PDF
        html2pdf()
            .set(options)
            .from(resume)
            .save()
            .then(function() {
                // Reset button state
                downloadBtn.textContent = 'Download PDF';
                downloadBtn.disabled = false;
            })
            .catch(function(error) {
                console.error('Error generating PDF:', error);
                downloadBtn.textContent = 'Download PDF';
                downloadBtn.disabled = false;
                alert('Error generating PDF. Please try again.');
            });
    });

    // Profile picture functionality temporarily removed for PDF testing
});

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});

// Add print functionality as backup
function printResume() {
    window.print();
}

// Keyboard shortcut for download (Ctrl+P)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        document.getElementById('downloadBtn').click();
    }
});
