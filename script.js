document.addEventListener('DOMContentLoaded', function() {
    const downloadBtn = document.getElementById('downloadBtn');
    const profilePicture = document.getElementById('profilePicture');
    const imageUpload = document.getElementById('imageUpload');
    const profileImage = document.getElementById('profileImage');
    const avatarPlaceholder = document.getElementById('avatarPlaceholder');
    const uploadOverlay = document.getElementById('uploadOverlay');

    downloadBtn.addEventListener('click', function() {
        // Show loading state
        downloadBtn.textContent = 'Generating PDF...';
        downloadBtn.disabled = true;

        // Get the resume element
        const resume = document.getElementById('resume');

        // Create a completely clean, isolated copy of the resume for PDF generation
        const pdfContainer = document.createElement('div');
        pdfContainer.style.cssText = `
            position: absolute;
            top: 0;
            left: -9999px;
            width: 8.5in;
            background: white;
            font-family: 'Inter', Arial, sans-serif;
            color: #333;
            line-height: 1.6;
            padding: 20px;
            margin: 0;
        `;

        // Clone the resume content
        const resumeClone = resume.cloneNode(true);
        resumeClone.style.cssText = `
            padding: 0;
            margin: 0;
            max-width: none;
            background: white;
        `;

        // Remove any upload overlays from the clone
        const uploadOverlays = resumeClone.querySelectorAll('.upload-overlay');
        uploadOverlays.forEach(overlay => overlay.remove());

        pdfContainer.appendChild(resumeClone);
        document.body.appendChild(pdfContainer);

        // Configure PDF options with minimal margins
        const options = {
            margin: [0.3, 0.5, 0.5, 0.5],
            filename: 'Jalan_McRae_Resume.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 2,
                useCORS: true,
                letterRendering: true
            },
            jsPDF: {
                unit: 'in',
                format: 'letter',
                orientation: 'portrait'
            }
        };

        // Generate PDF from the clean copy
        html2pdf()
            .set(options)
            .from(pdfContainer)
            .save()
            .then(() => {
                // Remove the temporary container
                document.body.removeChild(pdfContainer);
            })
            .then(function() {
                // Reset button state
                downloadBtn.textContent = 'Download PDF';
                downloadBtn.disabled = false;
            })
            .catch(function(error) {
                console.error('Error generating PDF:', error);
                // Clean up the temporary container if there's an error
                if (document.body.contains(pdfContainer)) {
                    document.body.removeChild(pdfContainer);
                }
                // Reset button state
                downloadBtn.textContent = 'Download PDF';
                downloadBtn.disabled = false;
                alert('Error generating PDF. Please try again.');
            });
    });

    // Image upload functionality
    profilePicture.addEventListener('click', function() {
        imageUpload.click();
    });

    imageUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Please select an image smaller than 5MB.');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                profileImage.src = e.target.result;
                profileImage.style.display = 'block';
                avatarPlaceholder.style.display = 'none';
                uploadOverlay.style.display = 'none';

                // Store image in localStorage for persistence
                localStorage.setItem('resumeProfileImage', e.target.result);
            };
            reader.readAsDataURL(file);
        }
    });

    // Load saved image on page load
    const savedImage = localStorage.getItem('resumeProfileImage');
    if (savedImage) {
        profileImage.src = savedImage;
        profileImage.style.display = 'block';
        avatarPlaceholder.style.display = 'none';
        uploadOverlay.style.display = 'none';
    }
});

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});

// Add print functionality as backup
function printResume() {
    window.print();
}

// Keyboard shortcut for download (Ctrl+P)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        document.getElementById('downloadBtn').click();
    }
});
