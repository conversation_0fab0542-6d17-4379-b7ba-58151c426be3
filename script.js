document.addEventListener('DOMContentLoaded', function() {
    const downloadBtn = document.getElementById('downloadBtn');
    const profilePicture = document.getElementById('profilePicture');
    const imageUpload = document.getElementById('imageUpload');
    const profileImage = document.getElementById('profileImage');
    const avatarPlaceholder = document.getElementById('avatarPlaceholder');
    const uploadOverlay = document.getElementById('uploadOverlay');

    downloadBtn.addEventListener('click', function() {
        // Show loading state
        downloadBtn.textContent = 'Generating PDF...';
        downloadBtn.disabled = true;

        // Get the resume element
        const resume = document.getElementById('resume');

        // Force scroll to top before PDF generation
        window.scrollTo(0, 0);

        // Store original styles for restoration
        const originalBodyStyle = document.body.style.cssText;
        const downloadSection = document.querySelector('.download-section');
        const container = document.querySelector('.container');
        const originalContainerStyle = container.style.cssText;
        const originalResumeStyle = resume.style.cssText;

        // Fix body layout issues that could push content down
        document.body.style.display = 'block';
        document.body.style.justifyContent = 'initial';
        document.body.style.alignItems = 'initial';
        document.body.style.margin = '0';
        document.body.style.padding = '0';

        // Completely hide and collapse the download section
        downloadSection.style.display = 'none';
        downloadSection.style.height = '0';
        downloadSection.style.overflow = 'hidden';

        // Reset container to eliminate any layout interference
        container.style.margin = '0';
        container.style.padding = '0';
        container.style.boxShadow = 'none';
        container.style.display = 'block';
        container.style.justifyContent = 'initial';
        container.style.alignItems = 'initial';

        // Start with minimal resume padding to test layout
        resume.style.margin = '0';
        resume.style.padding = '0'; // Start with zero padding to test

        // Configure PDF options with minimal margins
        const options = {
            margin: [0.2, 0.4, 0.4, 0.4], // Very small top margin
            filename: 'Jalan_McRae_Resume.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 2,
                useCORS: true,
                letterRendering: true,
                scrollX: 0,
                scrollY: 0
            },
            jsPDF: {
                unit: 'in',
                format: 'letter',
                orientation: 'portrait'
            }
        };

        // Generate and download PDF
        html2pdf()
            .set(options)
            .from(resume)
            .save()
            .then(() => {
                // Restore original styles after PDF generation
                document.body.style.cssText = originalBodyStyle;
                downloadSection.style.display = '';
                downloadSection.style.height = '';
                downloadSection.style.overflow = '';
                container.style.cssText = originalContainerStyle;
                resume.style.cssText = originalResumeStyle;
            })
            .then(function() {
                // Reset button state
                downloadBtn.textContent = 'Download PDF';
                downloadBtn.disabled = false;
            })
            .catch(function(error) {
                console.error('Error generating PDF:', error);
                // Restore original styles even if there's an error
                document.body.style.cssText = originalBodyStyle;
                downloadSection.style.display = '';
                downloadSection.style.height = '';
                downloadSection.style.overflow = '';
                container.style.cssText = originalContainerStyle;
                resume.style.cssText = originalResumeStyle;
                // Reset button state
                downloadBtn.textContent = 'Download PDF';
                downloadBtn.disabled = false;
                alert('Error generating PDF. Please try again.');
            });
    });

    // Image upload functionality
    profilePicture.addEventListener('click', function() {
        imageUpload.click();
    });

    imageUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Please select an image smaller than 5MB.');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                profileImage.src = e.target.result;
                profileImage.style.display = 'block';
                avatarPlaceholder.style.display = 'none';
                uploadOverlay.style.display = 'none';

                // Store image in localStorage for persistence
                localStorage.setItem('resumeProfileImage', e.target.result);
            };
            reader.readAsDataURL(file);
        }
    });

    // Load saved image on page load
    const savedImage = localStorage.getItem('resumeProfileImage');
    if (savedImage) {
        profileImage.src = savedImage;
        profileImage.style.display = 'block';
        avatarPlaceholder.style.display = 'none';
        uploadOverlay.style.display = 'none';
    }
});

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});

// Add print functionality as backup
function printResume() {
    window.print();
}

// Keyboard shortcut for download (Ctrl+P)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        document.getElementById('downloadBtn').click();
    }
});
