document.addEventListener('DOMContentLoaded', function() {
    const downloadBtn = document.getElementById('downloadBtn');
    const profilePicture = document.getElementById('profilePicture');
    const imageUpload = document.getElementById('imageUpload');
    const profileImage = document.getElementById('profileImage');
    const avatarPlaceholder = document.getElementById('avatarPlaceholder');
    const uploadOverlay = document.getElementById('uploadOverlay');

    downloadBtn.addEventListener('click', function() {
        // Show loading state
        downloadBtn.textContent = 'Generating PDF...';
        downloadBtn.disabled = true;

        // Get the resume element
        const resume = document.getElementById('resume');

        // Temporarily hide the download section and reset styles for clean PDF
        const downloadSection = document.querySelector('.download-section');
        const container = document.querySelector('.container');

        // Store original styles
        const originalDownloadDisplay = downloadSection.style.display;
        const originalContainerStyle = container.style.cssText;
        const originalResumeStyle = resume.style.cssText;

        // Apply clean styles for PDF generation
        downloadSection.style.display = 'none';
        container.style.cssText = `
            max-width: none;
            margin: 0;
            padding: 0;
            background: white;
            box-shadow: none;
        `;
        resume.style.cssText = `
            padding: 20px;
            margin: 0;
            background: white;
            max-width: 8.5in;
        `;

        // Hide upload overlays and profile picture temporarily for PDF
        const uploadOverlays = document.querySelectorAll('.upload-overlay');
        const profilePictureElement = document.querySelector('.profile-picture');

        uploadOverlays.forEach(overlay => {
            overlay.style.display = 'none';
        });

        // Temporarily hide the entire profile picture section
        const originalProfileDisplay = profilePictureElement.style.display;
        profilePictureElement.style.display = 'none';

        // Configure PDF options with minimal margins
        const options = {
            margin: [0.3, 0.5, 0.5, 0.5],
            filename: 'Jalan_McRae_Resume.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 2,
                useCORS: true,
                letterRendering: true
            },
            jsPDF: {
                unit: 'in',
                format: 'letter',
                orientation: 'portrait'
            }
        };

        // Generate PDF from the styled resume
        html2pdf()
            .set(options)
            .from(resume)
            .save()
            .then(() => {
                // Restore original styles
                downloadSection.style.display = originalDownloadDisplay;
                container.style.cssText = originalContainerStyle;
                resume.style.cssText = originalResumeStyle;
                profilePictureElement.style.display = originalProfileDisplay;
                uploadOverlays.forEach(overlay => {
                    overlay.style.display = '';
                });
            })
            .then(function() {
                // Reset button state
                downloadBtn.textContent = 'Download PDF';
                downloadBtn.disabled = false;
            })
            .catch(function(error) {
                console.error('Error generating PDF:', error);
                // Restore original styles even if there's an error
                downloadSection.style.display = originalDownloadDisplay;
                container.style.cssText = originalContainerStyle;
                resume.style.cssText = originalResumeStyle;
                profilePictureElement.style.display = originalProfileDisplay;
                uploadOverlays.forEach(overlay => {
                    overlay.style.display = '';
                });
                // Reset button state
                downloadBtn.textContent = 'Download PDF';
                downloadBtn.disabled = false;
                alert('Error generating PDF. Please try again.');
            });
    });

    // Image upload functionality
    profilePicture.addEventListener('click', function() {
        imageUpload.click();
    });

    imageUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Please select an image smaller than 5MB.');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                profileImage.src = e.target.result;
                profileImage.style.display = 'block';
                avatarPlaceholder.style.display = 'none';
                uploadOverlay.style.display = 'none';

                // Store image in localStorage for persistence
                localStorage.setItem('resumeProfileImage', e.target.result);
            };
            reader.readAsDataURL(file);
        }
    });

    // Load saved image on page load
    const savedImage = localStorage.getItem('resumeProfileImage');
    if (savedImage) {
        profileImage.src = savedImage;
        profileImage.style.display = 'block';
        avatarPlaceholder.style.display = 'none';
        uploadOverlay.style.display = 'none';
    }
});

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});

// Add print functionality as backup
function printResume() {
    window.print();
}

// Keyboard shortcut for download (Ctrl+P)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        document.getElementById('downloadBtn').click();
    }
});
