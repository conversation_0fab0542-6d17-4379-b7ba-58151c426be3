# AI Resume Builder

An intelligent resume builder powered by OpenRouter AI that helps you create professional resumes with AI-enhanced content.

## 🚀 Features

### Core Features
- **AI-Powered Content Generation**: Generate professional summaries, enhance job descriptions, and get skill suggestions
- **Multiple Templates**: Choose from Modern, Classic, Minimal, and Creative designs
- **Real-time Preview**: See your resume update as you type
- **PDF Export**: Download high-quality PDFs ready for job applications
- **Auto-save**: Never lose your progress with automatic saving
- **Responsive Design**: Works perfectly on desktop and mobile devices

### AI Capabilities
- **Professional Summary Generation**: AI creates compelling summaries based on your experience
- **Job Description Enhancement**: Transform basic job descriptions into achievement-focused content
- **Skills Suggestions**: Get relevant skill recommendations based on your background
- **Job Optimization**: Tailor your resume to specific job descriptions for better ATS compatibility

## 🎯 Getting Started

### Prerequisites
- Modern web browser
- OpenRouter API key (get one at [OpenRouter.ai](https://openrouter.ai))

### Installation

1. Clone or download this repository
2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3000`

### Alternative (No Installation Required)
Simply open the `index.html` file directly in your web browser.

## 📖 Usage

### Getting Started
1. **Landing Page**: Choose between viewing a sample resume or building your own
2. **API Setup**: Enter your OpenRouter API key in the builder
3. **Fill Information**: Add your personal details, experience, education, and skills
4. **AI Enhancement**: Use AI features to improve your content
5. **Export**: Download your resume as a PDF

### AI Features Usage
- **Generate Summary**: Click "Generate with AI" in the summary section
- **Enhance Job Descriptions**: Use the "Enhance with AI" button for each job
- **Get Skill Suggestions**: Click "AI Suggestions" in the skills section
- **Optimize for Jobs**: Paste a job description and click "Optimize Resume"

## 🎨 Templates

### Modern Template
- Clean, contemporary design
- Blue accent colors
- Grid-based layout
- Perfect for tech and business roles

### Classic Template
- Traditional, professional appearance
- Serif fonts
- Centered header
- Ideal for conservative industries

### Minimal Template
- Clean, minimalist design
- Subtle styling
- Maximum readability
- Great for creative fields

### Creative Template
- Eye-catching gradient header
- Modern styling
- Unique visual elements
- Perfect for design and marketing roles

## 🔧 Technical Details

### Technologies Used
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **AI Integration**: OpenRouter API with Claude 3.5 Sonnet
- **PDF Generation**: html2pdf.js
- **Fonts**: Inter font family from Google Fonts
- **Icons**: Font Awesome 6

### File Structure
```
├── index.html              # Landing page
├── builder.html            # AI resume builder interface
├── resume.html             # Sample resume
├── builder-style.css       # Builder interface styles
├── landing-style.css       # Landing page styles
├── style.css              # Resume template styles
├── builder-script.js      # Main application logic
├── script.js              # Resume functionality
└── README.md              # This file
```

### Browser Support
- Chrome (recommended)
- Firefox
- Safari
- Edge

## 🔑 API Configuration

This application uses OpenRouter AI for content generation. You'll need to:

1. Sign up at [OpenRouter.ai](https://openrouter.ai)
2. Get your API key
3. Enter it in the builder interface
4. The key is stored locally in your browser

## 🎯 Customization

### Adding New Templates
1. Create a new template method in `builder-script.js`
2. Add template styles in the `getTemplateStyles` method
3. Update the template selector in `builder.html`

### Modifying AI Prompts
Edit the prompt building methods in `builder-script.js`:
- `buildSummaryPrompt()`
- `buildSkillsPrompt()`
- `buildOptimizationPrompt()`
- `buildJobEnhancementPrompt()`

## 📱 Responsive Design

The application is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- All screen sizes

## 🔒 Privacy & Security

- All data is stored locally in your browser
- API keys are stored securely in localStorage
- No data is sent to external servers except OpenRouter for AI processing
- You can clear all data by clearing your browser's localStorage

## 🐛 Troubleshooting

### Common Issues
1. **AI features not working**: Check your OpenRouter API key
2. **PDF export issues**: Ensure you have content in your resume
3. **Template not loading**: Try refreshing the page
4. **Mobile display issues**: Ensure you're using a modern browser

## 📄 License

MIT License - feel free to use this project for personal or commercial purposes.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you encounter any issues or have questions, please create an issue in the repository.
