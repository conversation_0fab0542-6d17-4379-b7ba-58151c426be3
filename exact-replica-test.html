<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exact Resume Replica Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div class="resume" id="resume">
            <!-- Header Section -->
            <header class="header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="name">JALAN MCRAE</h1>
                        <h2 class="title">Customer Success Manager</h2>
                        <div class="contact-info">
                            <div class="contact-item">
                                <span class="icon">📞</span>
                                <span>+19105805253</span>
                            </div>
                            <div class="contact-item">
                                <span class="icon">✉️</span>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <span class="icon">📍</span>
                                <span>Raeford, NC</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-right">
                        <div class="profile-picture" id="profilePicture">
                            <div class="avatar-placeholder" id="avatarPlaceholder"></div>
                            <img id="profileImage" class="profile-image" style="display: none;" alt="Profile Picture">
                            <div class="upload-overlay" id="uploadOverlay">
                                <span class="upload-text">Click to upload photo</span>
                            </div>
                        </div>
                        <input type="file" id="imageUpload" accept="image/*" style="display: none;">
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Left Column -->
                <div class="left-column">
                    <!-- Experience Section -->
                    <section class="section">
                        <h3 class="section-title">EXPERIENCE</h3>

                        <div class="job">
                            <h4 class="job-title">Customer Success Manager</h4>
                            <div class="company">Figure</div>
                            <div class="job-meta">
                                <span class="date">05/2024 - Present</span>
                                <span class="location">Charlotte, NC</span>
                            </div>
                            <ul class="job-description">
                                <li>Manage a portfolio of enterprise SaaS clients, driving product adoption and optimizing account health to improve retention and reduce churn</li>
                                <li>Collaborate with Product and Engineering teams to refine AI/ML-driven support tools (e.g., chatbots), increasing support efficiency and customer satisfaction</li>
                                <li>Lead and mentor a team of Customer Success Originators, establishing KPIs and training programs that consistently exceed CSAT and revenue goals</li>
                                <li>Develop tailored customer success plans and conduct executive business reviews, aligning stakeholder objectives with product capabilities to identify upsell opportunities and improve NPS</li>
                                <li>Deliver compliance and training services (such as video-narration sessions) to enhance customer trust and ensure accurate solution deployment</li>
                            </ul>
                        </div>

                        <div class="job">
                            <h4 class="job-title">Technical Support Engineer</h4>
                            <div class="company">Nintex</div>
                            <div class="job-meta">
                                <span class="date">02/2023 - 06/2025</span>
                                <span class="location">Remote</span>
                            </div>
                            <ul class="job-description">
                                <li>Delivered high-quality technical support for SaaS eSignature and workflow automation clients, resolving technical issues promptly and maintaining a customer satisfaction (CSAT) score above 90%</li>
                                <li>Identified upsell opportunities within the Nintex Process Platform, contributing to revenue growth by aligning client needs with additional solutions</li>
                                <li>Provided onboarding and training for clients on AI-powered workflow tools, accelerating time-to-value and increasing tool adoption rates</li>
                            </ul>
                        </div>
                    </section>
                </div>

                <!-- Right Column -->
                <div class="right-column">
                    <!-- Summary Section -->
                    <section class="section">
                        <h3 class="section-title">SUMMARY</h3>
                        <p class="summary-text">
                            Dynamic Customer Success Manager with experience in SaaS and AI-driven support environments.
                        </p>
                    </section>

                    <!-- Certification Section -->
                    <section class="section">
                        <h3 class="section-title">CERTIFICATION</h3>
                        <div class="certification-item">
                            <span class="cert-name">Comptia a+</span>
                        </div>
                        <div class="certification-item">
                            <span class="cert-name">Comptia network+</span>
                        </div>
                    </section>

                    <!-- Languages Section -->
                    <section class="section">
                        <h3 class="section-title">LANGUAGES</h3>
                        <div class="language-item">
                            <span class="language-name">English</span>
                            <span class="language-level">Advanced</span>
                            <div class="skill-dots">
                                <span class="dot filled"></span>
                                <span class="dot filled"></span>
                                <span class="dot filled"></span>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>

        <!-- Download Button -->
        <div class="download-section">
            <button id="downloadBtn" class="download-btn">Download Exact Replica PDF</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const downloadBtn = document.getElementById('downloadBtn');

            downloadBtn.addEventListener('click', function() {
                downloadBtn.textContent = 'Generating PDF...';
                downloadBtn.disabled = true;

                const resume = document.getElementById('resume');
                const container = document.querySelector('.container');
                const originalContainerStyle = container.style.cssText;

                // Apply the same fix we're using in our main app
                container.style.margin = '0 auto';

                const options = {
                    margin: [0.3, 0.5, 0.5, 0.5],
                    filename: 'exact-replica-test.pdf',
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: {
                        scale: 2,
                        useCORS: true,
                        letterRendering: true
                    },
                    jsPDF: {
                        unit: 'in',
                        format: 'letter',
                        orientation: 'portrait'
                    }
                };

                html2pdf()
                    .set(options)
                    .from(resume)
                    .save()
                    .then(function() {
                        container.style.cssText = originalContainerStyle;
                        downloadBtn.textContent = 'Download Exact Replica PDF';
                        downloadBtn.disabled = false;
                    })
                    .catch(function(error) {
                        console.error('Error generating PDF:', error);
                        container.style.cssText = originalContainerStyle;
                        downloadBtn.textContent = 'Download Exact Replica PDF';
                        downloadBtn.disabled = false;
                        alert('Error generating PDF. Please try again.');
                    });
            });
        });
    </script>
</body>
</html>
