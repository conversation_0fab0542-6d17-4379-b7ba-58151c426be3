* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 900px;
    margin: 20px auto;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.resume {
    padding: 40px;
    max-width: 8.5in; /* Standard letter paper width */
    margin: 0 auto; /* Center the resume */
}

/* Header Styles */
.header {
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.header-left {
    flex: 1;
}

.name {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 5px;
    letter-spacing: 1px;
}

.title {
    font-size: 1.2rem;
    color: #2196F3;
    font-weight: 500;
    margin-bottom: 20px;
}

.contact-info {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #666;
}

.contact-item .icon {
    color: #2196F3;
}

.header-right {
    margin-left: 20px;
}

.profile-picture {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.profile-picture:hover {
    transform: scale(1.05);
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(33, 150, 243, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.profile-picture:hover .upload-overlay {
    opacity: 1;
}

.upload-text {
    color: white;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    padding: 0 10px;
}

.avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #999;
    position: relative;
}

.avatar-placeholder::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #666;
}

.avatar-placeholder::after {
    content: '';
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 35px;
    height: 25px;
    border-radius: 50% 50% 0 0;
    background-color: #666;
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

/* Section Styles */
.section {
    margin-bottom: 30px;
}

.section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #000;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 2px solid #000;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Job Styles */
.job {
    margin-bottom: 25px;
}

.job-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #000;
    margin-bottom: 5px;
}

.company {
    color: #2196F3;
    font-weight: 500;
    margin-bottom: 5px;
}

.job-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: #666;
}

.job-description {
    list-style: none;
    padding-left: 0;
}

.job-description li {
    margin-bottom: 8px;
    padding-left: 15px;
    position: relative;
    font-size: 0.9rem;
    line-height: 1.5;
}

.job-description li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #2196F3;
    font-weight: bold;
}

/* Right Column Styles */
.summary-text {
    font-size: 0.9rem;
    line-height: 1.6;
    color: #444;
}

.certification-item {
    margin-bottom: 8px;
}

.cert-name {
    color: #2196F3;
    font-weight: 500;
}

.language-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.language-name {
    font-weight: 500;
}

.language-level {
    font-size: 0.9rem;
    color: #666;
}

.skill-dots {
    display: flex;
    gap: 3px;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ddd;
}

.dot.filled {
    background-color: #2196F3;
}

.skills-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.skill-tag {
    background-color: #f0f0f0;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    color: #333;
    border: 1px solid #ddd;
}

/* Download Section */
.download-section {
    text-align: center;
    padding: 20px;
    background-color: #f9f9f9;
}

.download-btn {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.download-btn:hover {
    background-color: #1976D2;
}

/* Print/PDF Styles */
@media print {
    body {
        background: white;
        margin: 0;
        padding: 0;
    }

    .container {
        box-shadow: none;
        margin: 0;
        padding: 0;
    }

    .download-section {
        display: none;
    }

    .resume {
        padding: 15px 20px 20px 20px; /* Reduced top padding from 20px to 15px */
        margin: 0;
    }

    .upload-overlay {
        display: none !important;
    }

    .profile-picture:hover {
        transform: none;
    }

    /* Ensure header starts at the very top */
    header {
        margin-top: 0;
        padding-top: 0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .header-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .header-right {
        margin: 20px 0 0 0;
    }

    .contact-info {
        justify-content: center;
    }

    .resume {
        padding: 20px;
    }
}
