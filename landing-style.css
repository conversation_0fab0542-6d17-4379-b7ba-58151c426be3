/* Landing Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.landing-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.landing-header {
    text-align: center;
    padding: 4rem 2rem 2rem;
    color: white;
}

.main-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.main-title i {
    font-size: 3rem;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main Content */
.landing-main {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Options Container */
.options-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 4rem;
}

.option-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.option-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

.option-card.featured {
    border: 3px solid #f093fb;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.featured-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: #ff6b6b;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.option-card.featured .card-icon {
    color: white;
}

.option-card h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.option-card p {
    margin-bottom: 1.5rem;
    opacity: 0.8;
}

.feature-list {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.feature-list li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature-list i {
    color: #10b981;
    font-size: 0.9rem;
}

.option-card.featured .feature-list i {
    color: rgba(255,255,255,0.9);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-2px);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Features Section */
.features-section {
    background: white;
    border-radius: 1rem;
    padding: 3rem 2rem;
    margin-bottom: 3rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.features-section h2 {
    text-align: center;
    font-size: 2.2rem;
    margin-bottom: 3rem;
    color: #1f2937;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 1.5rem;
}

.feature-item i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #1f2937;
}

.feature-item p {
    color: #6b7280;
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    text-align: center;
    background: rgba(255,255,255,0.1);
    border-radius: 1rem;
    padding: 3rem 2rem;
    color: white;
    backdrop-filter: blur(10px);
}

.cta-section h2 {
    font-size: 2.2rem;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Footer */
.landing-footer {
    text-align: center;
    padding: 2rem;
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .main-title i {
        font-size: 2rem;
    }
    
    .options-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .landing-main {
        padding: 1rem;
    }
    
    .option-card {
        padding: 1.5rem;
    }
    
    .features-section {
        padding: 2rem 1rem;
    }
    
    .cta-section {
        padding: 2rem 1rem;
    }
}

@media (max-width: 480px) {
    .landing-header {
        padding: 2rem 1rem 1rem;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .features-section h2,
    .cta-section h2 {
        font-size: 1.8rem;
    }
}
