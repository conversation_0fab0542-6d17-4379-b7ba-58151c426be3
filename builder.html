<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Resume Builder</title>
    <link rel="stylesheet" href="builder-style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-robot"></i>
                    AI Resume Builder
                </h1>
                <div class="header-actions">
                    <button class="btn btn-secondary" id="saveBtn">
                        <i class="fas fa-save"></i>
                        Save Progress
                    </button>
                    <button class="btn btn-primary" id="exportBtn">
                        <i class="fas fa-download"></i>
                        Export PDF
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-container">
            <!-- Sidebar Form -->
            <aside class="sidebar">
                <div class="form-container">
                    <div class="form-header">
                        <h2>Build Your Resume</h2>
                        <p>Fill in your information and let AI enhance your content</p>
                    </div>

                    <!-- API Key Section -->
                    <div class="form-section">
                        <h3>
                            <i class="fas fa-key"></i>
                            OpenRouter API Key
                        </h3>
                        <div class="input-group">
                            <input type="password" id="apiKey" placeholder="Enter your OpenRouter API key">
                            <button class="btn-icon" id="toggleApiKey" title="Show/Hide API Key">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <small class="help-text">
                            Get your API key from <a href="https://openrouter.ai" target="_blank">OpenRouter.ai</a>
                        </small>
                    </div>

                    <!-- Personal Information -->
                    <div class="form-section">
                        <h3>
                            <i class="fas fa-user"></i>
                            Personal Information
                        </h3>
                        <div class="form-grid">
                            <input type="text" id="fullName" placeholder="Full Name" required>
                            <input type="text" id="jobTitle" placeholder="Job Title">
                            <input type="email" id="email" placeholder="Email">
                            <input type="tel" id="phone" placeholder="Phone">
                            <input type="text" id="location" placeholder="Location">
                            <input type="url" id="linkedin" placeholder="LinkedIn URL">
                            <input type="url" id="website" placeholder="Website/Portfolio">
                        </div>
                    </div>

                    <!-- Professional Summary -->
                    <div class="form-section">
                        <h3>
                            <i class="fas fa-file-text"></i>
                            Professional Summary
                        </h3>
                        <textarea id="summary" placeholder="Write a brief professional summary or let AI generate one for you..." rows="4"></textarea>
                        <button class="btn btn-ai" id="generateSummary">
                            <i class="fas fa-magic"></i>
                            Generate with AI
                        </button>
                    </div>

                    <!-- Experience Section -->
                    <div class="form-section">
                        <h3>
                            <i class="fas fa-briefcase"></i>
                            Work Experience
                        </h3>
                        <div id="experienceContainer">
                            <!-- Experience items will be added dynamically -->
                        </div>
                        <button class="btn btn-secondary" id="addExperience">
                            <i class="fas fa-plus"></i>
                            Add Experience
                        </button>
                    </div>

                    <!-- Education Section -->
                    <div class="form-section">
                        <h3>
                            <i class="fas fa-graduation-cap"></i>
                            Education
                        </h3>
                        <div id="educationContainer">
                            <!-- Education items will be added dynamically -->
                        </div>
                        <button class="btn btn-secondary" id="addEducation">
                            <i class="fas fa-plus"></i>
                            Add Education
                        </button>
                    </div>

                    <!-- Skills Section -->
                    <div class="form-section">
                        <h3>
                            <i class="fas fa-cogs"></i>
                            Skills
                        </h3>
                        <div class="skills-input-container">
                            <input type="text" id="skillInput" placeholder="Add a skill and press Enter">
                            <button class="btn btn-ai" id="suggestSkills">
                                <i class="fas fa-lightbulb"></i>
                                AI Suggestions
                            </button>
                        </div>
                        <div id="skillsContainer" class="skills-container">
                            <!-- Skills will be added dynamically -->
                        </div>
                    </div>

                    <!-- Job Description Optimization -->
                    <div class="form-section">
                        <h3>
                            <i class="fas fa-target"></i>
                            Job Description Optimization
                        </h3>
                        <textarea id="jobDescription" placeholder="Paste a job description here to optimize your resume for this specific role..." rows="4"></textarea>
                        <button class="btn btn-ai" id="optimizeResume">
                            <i class="fas fa-rocket"></i>
                            Optimize Resume
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Resume Preview -->
            <main class="preview-container">
                <div class="preview-header">
                    <h2>Live Preview</h2>
                    <div class="template-selector">
                        <label for="templateSelect">Template:</label>
                        <select id="templateSelect">
                            <option value="modern">Modern</option>
                            <option value="classic">Classic</option>
                            <option value="minimal">Minimal</option>
                            <option value="creative">Creative</option>
                        </select>
                    </div>
                </div>
                
                <div class="resume-preview" id="resumePreview">
                    <!-- Resume content will be generated here -->
                    <div class="preview-placeholder">
                        <i class="fas fa-file-alt"></i>
                        <h3>Your Resume Preview</h3>
                        <p>Start filling in your information to see your resume come to life!</p>
                    </div>
                </div>
            </main>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner"></div>
                <p>AI is working on your resume...</p>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container" id="toastContainer"></div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script src="builder-script.js"></script>
</body>
</html>
