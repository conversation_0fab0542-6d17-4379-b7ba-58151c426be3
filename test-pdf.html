<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border: 1px solid #ccc;
        }
        
        .content {
            background: white;
            padding: 20px;
        }
        
        h1 {
            margin: 0 0 20px 0;
            color: #333;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        
        p {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .download-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            margin: 20px 0;
        }
        
        .download-btn:hover {
            background-color: #1976D2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <button class="download-btn" onclick="generatePDF()">Download Test PDF</button>
        
        <div id="content" class="content">
            <h1>PDF Test Document</h1>
            <p>This is a simple test to see where content appears in the PDF.</p>
            <p>If you see this text starting near the top of the PDF page, then html2pdf is working correctly.</p>
            <p>If there's a lot of white space above this content, then we have a positioning issue.</p>
            
            <h2>Test Content</h2>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            
            <h2>More Content</h2>
            <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
        </div>
    </div>

    <script>
        function generatePDF() {
            const element = document.getElementById('content');
            
            const options = {
                margin: [0.3, 0.5, 0.5, 0.5],
                filename: 'test-document.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: {
                    scale: 2,
                    useCORS: true,
                    letterRendering: true
                },
                jsPDF: {
                    unit: 'in',
                    format: 'letter',
                    orientation: 'portrait'
                }
            };

            html2pdf()
                .set(options)
                .from(element)
                .save();
        }
    </script>
</body>
</html>
