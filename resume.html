<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jalan McRae - Resume</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="resume" id="resume">
            <!-- Header Section -->
            <header class="header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="name">JALAN MCRAE</h1>
                        <h2 class="title">Customer Success Manager</h2>
                        <div class="contact-info">
                            <div class="contact-item">
                                <span class="icon">📞</span>
                                <span>+19105805253</span>
                            </div>
                            <div class="contact-item">
                                <span class="icon">✉️</span>
                                <span>jalanm<PERSON><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <span class="icon">📍</span>
                                <span><PERSON>ford, NC</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-right">
                        <div class="profile-picture" id="profilePicture">
                            <div class="avatar-placeholder" id="avatarPlaceholder"></div>
                            <img id="profileImage" class="profile-image" style="display: none;" alt="Profile Picture">
                            <div class="upload-overlay" id="uploadOverlay">
                                <span class="upload-text">Click to upload photo</span>
                            </div>
                        </div>
                        <input type="file" id="imageUpload" accept="image/*" style="display: none;">
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Left Column -->
                <div class="left-column">
                    <!-- Experience Section -->
                    <section class="section">
                        <h3 class="section-title">EXPERIENCE</h3>

                        <div class="job">
                            <h4 class="job-title">Customer Success Manager</h4>
                            <div class="company">Figure</div>
                            <div class="job-meta">
                                <span class="date">May 2024 to Present</span>
                                <span class="location">Charlotte, NC</span>
                            </div>
                            <ul class="job-description">
                                <li>Manage a portfolio of enterprise SaaS clients, driving product adoption and optimizing account health to improve retention and reduce churn</li>
                                <li>Collaborate with Product and Engineering teams to refine AI/ML-driven support tools (e.g., chatbots), increasing support efficiency and customer satisfaction</li>
                                <li>Lead and mentor a team of Customer Success Originators, establishing KPIs and training programs that consistently exceed CSAT and revenue goals</li>
                                <li>Develop tailored customer success plans and conduct executive business reviews, aligning stakeholder objectives with product capabilities to identify upsell opportunities and improve NPS</li>
                                <li>Deliver compliance and training services (such as video-narration sessions) to enhance customer trust and ensure accurate solution deployment</li>
                            </ul>
                        </div>

                        <div class="job">
                            <h4 class="job-title">Technical Support Engineer</h4>
                            <div class="company">Nintex</div>
                            <div class="job-meta">
                                <span class="date">February 2023 to April 2024</span>
                                <span class="location">Remote</span>
                            </div>
                            <ul class="job-description">
                                <li>Delivered high-quality technical support for SaaS eSignature and workflow automation clients, resolving technical issues promptly and maintaining a customer satisfaction (CSAT) score above 90%</li>
                                <li>Identified upsell opportunities within the Nintex Process Platform, contributing to revenue growth by aligning client needs with additional solutions</li>
                                <li>Provided onboarding and training for clients on AI-powered workflow tools, accelerating time-to-value and increasing tool adoption rates</li>
                                <li>Collaborated cross-functionally with Sales, Product, and Engineering teams to communicate customer feedback and drive timely product enhancements</li>
                                <li>Consistently exceeded performance targets and SLA requirements through proactive troubleshooting and client communication</li>
                            </ul>
                        </div>

                        <div class="job">
                            <h4 class="job-title">Lowes Lumber Sales Associate</h4>
                            <div class="company">Lowe's Home Improvement</div>
                            <div class="job-meta">
                                <span class="date">January 2019 to February 2023</span>
                                <span class="location">Fayetteville, NC</span>
                            </div>
                            <ul class="job-description">
                                <li>Provided exceptional customer service in the lumber department, advising on materials and project planning to boost sales and satisfaction</li>
                                <li>Managed inventory, receiving shipments and organizing stock displays to ensure product availability and operational efficiency</li>
                                <li>Safely operated forklifts and saw equipment to fulfill custom orders, adhering to safety protocols and maintaining accuracy</li>
                                <li>Collaborated with team members to improve workflow and store operations, including training new hires on departmental procedures and safety standards</li>
                            </ul>
                        </div>
                    </section>
                </div>

                <!-- Right Column -->
                <div class="right-column">
                    <!-- Summary Section -->
                    <section class="section">
                        <h3 class="section-title">SUMMARY</h3>
                        <p class="summary-text">
                            Dynamic Customer Success Manager with experience in SaaS and AI-driven support environments. • Proven track record driving adoption, retention, and revenue growth through strategic lifecycle management and proactive client health monitoring. ⚫ Skilled at client onboarding, stakeholder engagement, and leveraging CSAT/NPS metrics to reduce churn and enhance customer satisfaction. • Adept at leading cross-functional teams to exceed performance goals and deliver exceptional customer experiences. Authorized to work in the US for any employer
                        </p>
                    </section>

                    <!-- Certification Section -->
                    <section class="section">
                        <h3 class="section-title">CERTIFICATION</h3>
                        <div class="certification-item">
                            <span class="cert-name">Comptia a+</span>
                        </div>
                        <div class="certification-item">
                            <span class="cert-name">Comptia network+</span>
                        </div>
                    </section>

                    <!-- Languages Section -->
                    <section class="section">
                        <h3 class="section-title">LANGUAGES</h3>
                        <div class="language-item">
                            <span class="language-name">English</span>
                            <span class="language-level">Advanced</span>
                            <div class="skill-dots">
                                <span class="dot filled"></span>
                                <span class="dot filled"></span>
                                <span class="dot filled"></span>
                            </div>
                        </div>
                    </section>

                    <!-- Skills Section -->
                    <section class="section">
                        <h3 class="section-title">SKILLS</h3>
                        <div class="skills-grid">
                            <span class="skill-tag">CSat</span>
                            <span class="skill-tag">HTML</span>
                            <span class="skill-tag">JavaScript</span>
                            <span class="skill-tag">Linux</span>
                            <span class="skill-tag">Salesforce</span>
                            <span class="skill-tag">SharePoint</span>
                            <span class="skill-tag">Slack</span>
                            <span class="skill-tag">SQL</span>
                            <span class="skill-tag">Training Programs</span>
                            <span class="skill-tag">Gmail</span>
                        </div>
                    </section>
                </div>
            </div>
        </div>

        <!-- Download Button -->
        <div class="download-section">
            <button id="downloadBtn" class="download-btn">Download PDF</button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
