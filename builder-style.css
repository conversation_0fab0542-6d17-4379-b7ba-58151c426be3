/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', Arial, sans-serif;
    background-color: #f8fafc;
    color: #334155;
    line-height: 1.6;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-ai {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
}

.btn-ai:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4);
}

.btn-icon {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
}

.btn-icon:hover {
    background-color: #f3f4f6;
    color: #374151;
}

/* Main Container */
.main-container {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Sidebar */
.sidebar {
    width: 400px;
    background: white;
    border-right: 1px solid #e5e7eb;
    height: calc(100vh - 80px);
    overflow-y: auto;
}

.form-container {
    padding: 2rem;
}

.form-header {
    margin-bottom: 2rem;
    text-align: center;
}

.form-header h2 {
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #6b7280;
    font-size: 0.9rem;
}

/* Form Sections */
.form-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #f3f4f6;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    color: #1f2937;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-section h3 i {
    color: #3b82f6;
}

/* Form Inputs */
.form-grid {
    display: grid;
    gap: 1rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

input, textarea, select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-group input {
    padding-right: 3rem;
}

.input-group .btn-icon {
    position: absolute;
    right: 0.5rem;
}

.help-text {
    color: #6b7280;
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.help-text a {
    color: #3b82f6;
    text-decoration: none;
}

.help-text a:hover {
    text-decoration: underline;
}

/* Skills */
.skills-input-container {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.skills-input-container input {
    flex: 1;
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.skill-tag {
    background-color: #eff6ff;
    color: #1d4ed8;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.skill-tag .remove-skill {
    background: none;
    border: none;
    color: #1d4ed8;
    cursor: pointer;
    font-size: 0.7rem;
}

/* Preview Container */
.preview-container {
    flex: 1;
    background: #f8fafc;
    padding: 2rem;
    height: calc(100vh - 80px);
    overflow-y: auto;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.preview-header h2 {
    color: #1f2937;
}

.template-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.template-selector label {
    font-weight: 500;
    color: #374151;
}

.resume-preview {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    min-height: 800px;
    max-width: 8.5in;
    margin: 0 auto;
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #9ca3af;
    text-align: center;
}

.preview-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.preview-placeholder h3 {
    margin-bottom: 0.5rem;
    color: #6b7280;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 0.5rem;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1001;
}

.toast {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-left: 4px solid #10b981;
    animation: slideIn 0.3s ease;
}

.toast.error {
    border-left-color: #ef4444;
}

.toast.warning {
    border-left-color: #f59e0b;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
    }
    
    .preview-container {
        height: auto;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .form-container {
        padding: 1rem;
    }
    
    .preview-container {
        padding: 1rem;
    }
}
