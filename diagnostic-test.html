<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Diagnostic Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 20px auto;  /* This is the suspected culprit */
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .resume {
            padding: 40px;  /* Large padding like our app */
            max-width: 8.5in;
            margin: 0 auto;
        }

        .header {
            margin-bottom: 30px;
        }

        .header-content {
            display: flex;  /* Flexbox layout like our app */
            justify-content: space-between;
            align-items: flex-start;
        }

        .header-left {
            flex: 1;
        }

        .name {
            font-size: 2.5rem;
            font-weight: 700;
            color: #000;
            margin-bottom: 5px;
            letter-spacing: 1px;
        }

        .title {
            font-size: 1.2rem;
            color: #2196F3;
            font-weight: 500;
            margin-bottom: 20px;
        }

        .main-content {
            display: grid;  /* CSS Grid like our app */
            grid-template-columns: 2fr 1fr;
            gap: 40px;
        }

        .download-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            margin: 20px;
        }

        .test-content {
            background: #f9f9f9;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h2>Diagnostic Tests</h2>
        <button class="download-btn" onclick="testBasic()">Test 1: Basic Content</button>
        <button class="download-btn" onclick="testWithContainer()">Test 2: With Container</button>
        <button class="download-btn" onclick="testWithFlex()">Test 3: With Flexbox Header</button>
        <button class="download-btn" onclick="testWithGrid()">Test 4: With CSS Grid</button>
        <button class="download-btn" onclick="testFullStructure()">Test 5: Full Resume Structure</button>
    </div>

    <!-- Test 1: Basic content (like our working test) -->
    <div id="basic" style="display: none;">
        <h1>Basic Test</h1>
        <p>This should work fine, just like our simple test.</p>
    </div>

    <!-- Test 2: With container margin -->
    <div class="container" id="withContainer" style="display: none;">
        <div style="padding: 20px;">
            <h1>Container Test</h1>
            <p>Testing if container margin: 20px auto causes issues.</p>
        </div>
    </div>

    <!-- Test 3: With flexbox header -->
    <div class="container" id="withFlex" style="display: none;">
        <div class="resume">
            <header class="header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="name">FLEXBOX TEST</h1>
                        <h2 class="title">Testing Flexbox Layout</h2>
                    </div>
                </div>
            </header>
            <p>Testing if flexbox header causes positioning issues.</p>
        </div>
    </div>

    <!-- Test 4: With CSS Grid -->
    <div class="container" id="withGrid" style="display: none;">
        <div class="resume">
            <header class="header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="name">GRID TEST</h1>
                        <h2 class="title">Testing CSS Grid Layout</h2>
                    </div>
                </div>
            </header>
            <div class="main-content">
                <div>
                    <h3>Left Column</h3>
                    <p>Testing CSS Grid layout impact on PDF positioning.</p>
                </div>
                <div>
                    <h3>Right Column</h3>
                    <p>Grid content here.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Test 5: Full structure like our resume -->
    <div class="container" id="fullStructure" style="display: none;">
        <div class="resume">
            <header class="header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="name">FULL STRUCTURE TEST</h1>
                        <h2 class="title">Complete Resume Layout</h2>
                    </div>
                    <div style="width: 120px; height: 120px; background: #e0e0e0; border-radius: 50%;"></div>
                </div>
            </header>
            <div class="main-content">
                <div>
                    <h3>EXPERIENCE</h3>
                    <p>This mimics our full resume structure to identify the exact cause.</p>
                </div>
                <div>
                    <h3>SUMMARY</h3>
                    <p>Right column content here.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function generatePDF(elementId, filename) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            const options = {
                margin: [0.3, 0.5, 0.5, 0.5],
                filename: filename,
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: {
                    scale: 2,
                    useCORS: true,
                    letterRendering: true
                },
                jsPDF: {
                    unit: 'in',
                    format: 'letter',
                    orientation: 'portrait'
                }
            };

            html2pdf()
                .set(options)
                .from(element)
                .save()
                .then(() => {
                    element.style.display = 'none';
                });
        }

        function testBasic() { generatePDF('basic', 'test1-basic.pdf'); }
        function testWithContainer() { generatePDF('withContainer', 'test2-container.pdf'); }
        function testWithFlex() { generatePDF('withFlex', 'test3-flexbox.pdf'); }
        function testWithGrid() { generatePDF('withGrid', 'test4-grid.pdf'); }
        function testFullStructure() { generatePDF('fullStructure', 'test5-full.pdf'); }
    </script>
</body>
</html>
